# 🚀 QUICKSTART - Novel端云协同推荐系统

本指南提供了Novel端云协同推荐系统的完整复现流程，基于LLM-SRec的实验设计，专注于模型训练和评估。

## 📋 环境配置

### 1. 创建环境
```bash
conda create -n novel-rec python=3.9
conda activate novel-rec
```

### 2. 安装依赖
```bash
cd Novel
pip install -r requirements.txt
```

### 3. 验证环境
```bash
python -c "import torch; print(f'PyTorch: {torch.__version__}')"
python -c "import transformers; print(f'Transformers: {transformers.__version__}')"
```

## 🗂️ 数据准备

### 1. 支持的数据集
- **Movies_and_TV**: Amazon 2023数据集，约1.7M交互
- **Industrial_and_Scientific**: Amazon 2023数据集，约0.5M交互
- **Beauty**: Amazon 2023数据集，约0.3M交互

### 2. 自动数据下载
数据集在首次运行时会自动下载和预处理：

```bash
# 数据会自动下载到 data/ 目录
# 预处理后的文件格式：
# - {dataset}_train.txt
# - {dataset}_valid.txt  
# - {dataset}_test.txt
```

## 🔧 模型训练

### GPU配置检查

首先验证您的GPU环境：

```bash
# 检查GPU状态
nvidia-smi

# 一键多GPU环境测试（推荐）
python -c "
from utils import GPUManager
gpu_manager = GPUManager()
print('🔧 GPU环境检查:')
gpu_available = gpu_manager.check_gpu_environment()
multi_gpu_ok = gpu_manager.test_multi_gpu()
config = gpu_manager.recommend_multi_gpu_config()
print('✅ GPU环境验证完成')
"

# 手动检查PyTorch GPU支持
python -c "
import torch
print(f'CUDA可用: {torch.cuda.is_available()}')
print(f'GPU数量: {torch.cuda.device_count()}')
for i in range(torch.cuda.device_count()):
    print(f'GPU {i}: {torch.cuda.get_device_name(i)}')
    print(f'  显存: {torch.cuda.get_device_properties(i).total_memory / 1024**3:.1f} GB')
"
```

### 阶段1：预训练CF-SRec端侧模型

基于SASRec架构的序列推荐模型预训练：

#### 单GPU训练
```bash
# 使用Movies_and_TV数据集（单GPU）
python run_experiment.py \
    --stage pretrain_cf_srec \
    --dataset Movies_and_TV \
    --device cuda:0 \
    --batch-size 128 \
    --num-epochs 200 \
    --hidden_units 64 \
    --num_blocks 2 \
    --maxlen 50

# 使用Industrial_and_Scientific数据集（较小，训练更快）
python run_experiment.py \
    --stage pretrain_cf_srec \
    --dataset Industrial_and_Scientific \
    --device cuda:0 \
    --batch-size 128 \
    --num-epochs 200
```

#### 多GPU训练（推荐用于双3090等配置）
```bash
# 双GPU DataParallel模式（推荐）
python run_experiment.py \
    --stage pretrain_cf_srec \
    --dataset Movies_and_TV \
    --device cuda \
    --multi_gpu \
    --batch-size 512 \
    --num-epochs 200 \
    --hidden_units 128 \
    --num_blocks 4 \
    --maxlen 128 \
    --fp16

# 双GPU DistributedDataParallel模式（更高效）
CUDA_VISIBLE_DEVICES=0,1 python -m torch.distributed.launch \
    --nproc_per_node=2 \
    run_experiment.py \
    --stage pretrain_cf_srec \
    --dataset Movies_and_TV \
    --distributed \
    --batch-size 256 \
    --gradient_accumulation_steps 2 \
    --num-epochs 200 \
    --hidden_units 128 \
    --num_blocks 4 \
    --fp16

# 使用新版torchrun（推荐）
CUDA_VISIBLE_DEVICES=0,1 torchrun \
    --nproc_per_node=2 \
    run_experiment.py \
    --stage pretrain_cf_srec \
    --dataset Movies_and_TV \
    --distributed \
    --batch-size 256 \
    --gradient_accumulation_steps 2 \
    --num-epochs 200 \
    --hidden_units 128 \
    --num_blocks 4 \
    --fp16
```

#### 双3090一键训练脚本（推荐）
```bash
# 使用更新后的快速启动脚本
chmod +x quick_start.sh
./quick_start.sh

# 或者直接运行双3090优化命令
python run_experiment.py \
    --stage pretrain_cf_srec \
    --dataset Movies_and_TV \
    --device cuda \
    --multi_gpu \
    --batch-size 512 \
    --num-epochs 200 \
    --hidden_units 128 \
    --num_blocks 4 \
    --maxlen 128 \
    --fp16 \
    --experiment-name "cf_srec_dual_3090"
```

**预期输出**：
- 模型保存在 `checkpoints/cf_srec_{dataset}/`
- 训练日志显示NDCG@10和Hit Rate@10指标
- 最佳模型在验证集上达到最优性能时保存
- 双3090配置下训练时间约1-1.5小时

### 阶段2：训练序列增强LLM

使用预训练的CF-SRec模型来增强LLM：

#### 单GPU训练
```bash
# 训练LLM推荐模型（单GPU）
python run_experiment.py \
    --stage train_llm \
    --dataset Movies_and_TV \
    --rec_pre_trained_data Movies_and_TV \
    --device cuda:0 \
    --batch_size 20 \
    --num_epochs 10 \
    --stage2_lr 0.0001 \
    --save_dir model_train
```

#### 多GPU训练
```bash
# 双GPU LLM训练（推荐配置）
python run_experiment.py \
    --stage train_llm \
    --dataset Movies_and_TV \
    --rec_pre_trained_data Movies_and_TV \
    --multi_gpu \
    --batch_size 40 \
    --gradient_accumulation_steps 4 \
    --num_epochs 10 \
    --stage2_lr 0.0001 \
    --fp16 \
    --save_dir model_train

# 使用DistributedDataParallel
CUDA_VISIBLE_DEVICES=0,1 python -m torch.distributed.launch \
    --nproc_per_node=2 \
    run_experiment.py \
    --stage train_llm \
    --dataset Movies_and_TV \
    --rec_pre_trained_data Movies_and_TV \
    --distributed \
    --batch_size 20 \
    --gradient_accumulation_steps 8 \
    --num_epochs 10 \
    --fp16
```

### 阶段3：端云协同训练

结合端侧CF-SRec和云端LLM进行协同训练：

#### 单GPU训练
```bash
# 端云协同训练（单GPU）
python run_experiment.py \
    --stage collaborative_training \
    --dataset Movies_and_TV \
    --rec_pre_trained_data Movies_and_TV \
    --device cuda:0 \
    --batch_size 16 \
    --num_epochs 20 \
    --enable_distillation \
    --privacy_protection
```

#### 多GPU训练
```bash
# 双GPU协同训练（推荐配置）
CUDA_VISIBLE_DEVICES=0,1 python run_experiment.py \
    --stage collaborative_training \
    --dataset Movies_and_TV \
    --rec_pre_trained_data Movies_and_TV \
    --multi_gpu \
    --batch_size 32 \
    --gradient_accumulation_steps 2 \
    --num_epochs 20 \
    --enable_distillation \
    --privacy_protection \
    --fp16

# 高性能配置（双3090优化）
CUDA_VISIBLE_DEVICES=0,1 python -m torch.distributed.launch \
    --nproc_per_node=2 \
    run_experiment.py \
    --stage collaborative_training \
    --dataset Movies_and_TV \
    --rec_pre_trained_data Movies_and_TV \
    --distributed \
    --batch_size 24 \
    --gradient_accumulation_steps 4 \
    --num_epochs 20 \
    --enable_distillation \
    --privacy_protection \
    --fp16
```

## ⚙️ 实验配置

### 1. 基本配置文件

创建实验配置文件 `config/experiments/movies_full.yaml`：

```yaml
# 数据配置
data:
  dataset: Movies_and_TV
  max_seq_length: 50
  train_batch_size: 128
  eval_batch_size: 64

# CF-SRec端侧模型配置
edge_model:
  hidden_units: 64
  num_blocks: 2
  num_heads: 1
  dropout_rate: 0.1
  maxlen: 50

# LLM云端模型配置  
cloud_model:
  model_name: "meta-llama/Llama-3.2-3B-Instruct"
  max_length: 512
  temperature: 0.7

# 训练配置
training:
  num_epochs: 200
  learning_rate: 0.001
  stage2_lr: 0.0001
  weight_decay: 0.0
  gradient_clip: 1.0

# 评估配置
evaluation:
  eval_users: 2000
  metrics: ['ndcg@10', 'hit_rate@10', 'recall@10']
  top_k_list: [5, 10, 20]
```

### 2. 快速测试配置

对于快速验证，使用较小的配置：

```bash
# 快速测试（约30分钟）
python run_experiment.py \
    --stage pretrain_cf_srec \
    --dataset Industrial_and_Scientific \
    --device cuda:0 \
    --batch_size 64 \
    --num_epochs 50 \
    --hidden_units 32 \
    --num_blocks 1
```

## 📊 模型评估

### 1. 单独评估CF-SRec
```bash
# 评估预训练的CF-SRec模型
python evaluate.py \
    --model_type cf_srec \
    --dataset Movies_and_TV \
    --model_path checkpoints/cf_srec_Movies_and_TV/best_model.pth \
    --eval_users 2000
```

### 2. 评估端云协同系统
```bash
# 评估完整的端云协同系统
python evaluate.py \
    --model_type collaborative \
    --dataset Movies_and_TV \
    --cf_srec_path checkpoints/cf_srec_Movies_and_TV/best_model.pth \
    --llm_path checkpoints/model_train/best_model.pth \
    --eval_users 2000
```

### 3. 性能基准测试
```bash
# 推理延迟测试
python benchmark.py \
    --model_type collaborative \
    --dataset Movies_and_TV \
    --batch_size 1 \
    --num_samples 1000
```

## 📈 预期结果

### CF-SRec端侧模型（Movies_and_TV）

#### 单GPU性能（RTX 3090）
- **NDCG@10**: ~0.18
- **Hit Rate@10**: ~0.32
- **训练时间**: ~2-3小时
- **推理延迟**: <10ms
- **批次大小**: 128
- **显存使用**: ~18GB

#### 多GPU性能（双RTX 3090）
- **NDCG@10**: ~0.20 (更大模型)
- **Hit Rate@10**: ~0.35 (更大模型)
- **训练时间**: ~1-1.5小时 (2x加速)
- **推理延迟**: <10ms
- **批次大小**: 512
- **显存使用**: ~20GB per GPU

### 端云协同系统（Movies_and_TV）

#### 单GPU性能（RTX 3090）
- **NDCG@10**: ~0.26 (+44% vs CF-SRec)
- **Hit Rate@10**: ~0.45 (+41% vs CF-SRec)
- **训练时间**: ~6-8小时
- **推理延迟**: 150-300ms
- **显存使用**: ~22GB

#### 多GPU性能（双RTX 3090）
- **NDCG@10**: ~0.28 (+56% vs CF-SRec)
- **Hit Rate@10**: ~0.48 (+50% vs CF-SRec)
- **训练时间**: ~3-4小时 (2x加速)
- **推理延迟**: 150-300ms
- **显存使用**: ~20GB per GPU

## 🖥️ 性能监控

### 实时监控GPU状态
```bash
# 监控GPU使用情况
watch -n 1 nvidia-smi

# 监控训练进度
tail -f checkpoints/movies_full/train.log

# 检查GPU内存使用
python -c "
import torch
if torch.cuda.is_available():
    for i in range(torch.cuda.device_count()):
        print(f'GPU {i} 内存使用:')
        print(f'  已分配: {torch.cuda.memory_allocated(i)/1024**3:.2f} GB')
        print(f'  已缓存: {torch.cuda.memory_reserved(i)/1024**3:.2f} GB')
"
```

### 多GPU训练验证
```bash
# 验证多GPU配置（整合到utils模块）
python -c "
from utils import GPUManager
gpu_manager = GPUManager()

print('🔧 GPU环境检查:')
gpu_available = gpu_manager.check_gpu_environment()
multi_gpu_ok = gpu_manager.test_multi_gpu()

print(f'GPU可用: {gpu_available}')
print(f'多GPU测试: {multi_gpu_ok}')

if gpu_available:
    config = gpu_manager.recommend_multi_gpu_config()
    print('推荐配置已生成')
"
```

## 🔍 故障排除

### 1. 单GPU内存不足
```bash
# 减少批次大小
--batch_size 32  # 替代 128

# 减少序列长度
--maxlen 20      # 替代 50

# 使用梯度累积
--gradient_accumulation_steps 4

# 启用混合精度训练
--fp16
```

### 2. 多GPU显存不足
```bash
# 减少每GPU批次大小
--batch_size 128  # 替代 256（双GPU）

# 增加梯度累积步数
--gradient_accumulation_steps 8

# 减少模型大小
--hidden_units 64    # 替代 128
--num_blocks 2       # 替代 4

# 启用混合精度训练
--fp16
```

### 3. 多GPU同步问题
```bash
# 检查NCCL后端
export NCCL_DEBUG=INFO

# 设置NCCL超时
export NCCL_TIMEOUT=1800

# 使用单机多卡模式
python -m torch.distributed.launch --nproc_per_node=2 run_experiment.py

# 检查GPU通信
nvidia-smi topo -m
```

### 4. 分布式训练错误
```bash
# 确保端口可用
export MASTER_PORT=29500

# 设置后端
export NCCL_BACKEND=nccl

# 检查CUDA版本兼容性
python -c "
import torch
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA版本: {torch.version.cuda}')
print(f'NCCL可用: {torch.distributed.is_nccl_available()}')
"
```

### 5. 数据下载失败
```bash
# 手动下载数据集
python -c "
from datasets import DatasetManager
dm = DatasetManager()
dm.download_dataset('Movies_and_TV')
"

# 设置代理（如果需要）
export HTTP_PROXY=http://your-proxy:port
export HTTPS_PROXY=http://your-proxy:port
```

### 6. 多GPU性能优化建议

#### 双3090优化配置
```bash
# 最优批次大小配置
CF-SRec预训练: --batch_size 512
LLM训练: --batch_size 40
协同训练: --batch_size 32

# 内存优化
--gradient_accumulation_steps 2-4
--fp16
--max_grad_norm 1.0

# 数据加载优化
--num_workers 8
--pin_memory
```

## 🎯 复现检查清单

- [ ] 环境配置完成，依赖安装成功
- [ ] 数据集自动下载和预处理完成
- [ ] CF-SRec模型训练完成，达到预期性能
- [ ] LLM模型训练完成
- [ ] 端云协同训练完成
- [ ] 评估结果符合论文报告的性能范围
- [ ] 推理延迟测试通过

## 📚 进阶使用

### 自定义数据集
```bash
# 添加新数据集
python add_dataset.py --name custom_dataset --path /path/to/data

# 使用自定义数据集训练
python run_experiment.py \
    --stage pretrain_cf_srec \
    --dataset custom_dataset \
    --device cuda:0
```

### 模型分析
```bash
# 分析训练结果
python analyze_results.py --experiment_dir checkpoints/model_train

# 可视化训练曲线
python plot_training.py --log_file checkpoints/model_train/train.log
```

**📖 更多详细信息请参考 `README.md` 和 `docs/` 目录中的技术文档。**
