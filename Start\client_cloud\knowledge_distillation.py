"""
智能知识蒸馏端云协同框架

本模块实现端云模型之间的智能知识蒸馏机制：
1. 云端知识回传：将云端LLM的知识蒸馏到端侧模型
2. 自适应蒸馏策略：根据模型性能动态调整蒸馏参数
3. 持续协同进化：实现端云模型的持续学习和优化
4. 知识质量评估：评估和筛选高质量的蒸馏知识

核心功能：
- 特征对齐：对齐端云模型的表示空间
- 知识传递：高效的知识蒸馏算法
- 性能监控：跟踪蒸馏效果和模型性能
- 自适应调整：动态优化蒸馏策略
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
import logging
import time
from collections import deque
import json

from .edge_cf_srec import EdgeCFSRec
from .cloud_llm_enhanced import CloudLLMEnhanced


@dataclass
class DistillationConfig:
    """知识蒸馏配置"""
    # 蒸馏参数
    temperature: float = 4.0  # 蒸馏温度
    alpha: float = 0.7  # 蒸馏损失权重
    beta: float = 0.3   # 任务损失权重

    # 双向蒸馏参数 (新增)
    enable_bidirectional: bool = True  # 启用双向蒸馏
    reverse_distillation_weight: float = 0.3  # 反向蒸馏权重
    mutual_info_weight: float = 0.2  # 互信息损失权重
    contrastive_weight: float = 0.1  # 对比学习权重

    # 特征对齐参数
    feature_alignment_weight: float = 0.5
    representation_dim: int = 64

    # 自适应参数
    performance_window: int = 100  # 性能评估窗口
    adaptation_threshold: float = 0.05  # 性能改进阈值
    max_temperature: float = 8.0
    min_temperature: float = 1.0

    # 训练参数
    distillation_epochs: int = 10
    learning_rate: float = 1e-4
    batch_size: int = 32

    # 质量控制参数
    knowledge_quality_threshold: float = 0.6
    max_knowledge_buffer_size: int = 1000


@dataclass
class DistillationMetrics:
    """蒸馏指标"""
    epoch: int
    distillation_loss: float
    task_loss: float
    total_loss: float
    student_accuracy: float
    teacher_accuracy: float
    knowledge_transfer_rate: float
    timestamp: float


class KnowledgeBuffer:
    """
    知识缓冲区
    
    存储和管理从云端获取的高质量知识
    """
    
    def __init__(self, max_size: int = 1000, quality_threshold: float = 0.6):
        self.max_size = max_size
        self.quality_threshold = quality_threshold
        self.buffer = deque(maxlen=max_size)
        self.quality_scores = deque(maxlen=max_size)
        
    def add_knowledge(
        self, 
        user_repr: torch.Tensor,
        teacher_output: torch.Tensor,
        quality_score: float,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        添加知识到缓冲区
        
        Args:
            user_repr: 用户表示
            teacher_output: 教师模型输出
            quality_score: 知识质量分数
            metadata: 元数据
        """
        if quality_score >= self.quality_threshold:
            knowledge_item = {
                'user_repr': user_repr.detach().cpu(),
                'teacher_output': teacher_output.detach().cpu(),
                'quality_score': quality_score,
                'metadata': metadata or {},
                'timestamp': time.time()
            }
            
            self.buffer.append(knowledge_item)
            self.quality_scores.append(quality_score)
    
    def sample_batch(self, batch_size: int) -> List[Dict[str, Any]]:
        """
        采样一批知识
        
        Args:
            batch_size: 批次大小
            
        Returns:
            知识批次
        """
        if len(self.buffer) < batch_size:
            return list(self.buffer)
        
        # 基于质量分数的加权采样
        weights = np.array(self.quality_scores)
        weights = weights / weights.sum()
        
        indices = np.random.choice(
            len(self.buffer), 
            size=batch_size, 
            replace=False, 
            p=weights
        )
        
        return [self.buffer[i] for i in indices]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取缓冲区统计信息"""
        if not self.buffer:
            return {
                'size': 0,
                'average_quality': 0.0,
                'max_quality': 0.0,
                'min_quality': 0.0
            }
        
        quality_scores = list(self.quality_scores)
        return {
            'size': len(self.buffer),
            'average_quality': np.mean(quality_scores),
            'max_quality': np.max(quality_scores),
            'min_quality': np.min(quality_scores)
        }


class AdaptiveDistillationStrategy:
    """
    自适应蒸馏策略
    
    根据模型性能动态调整蒸馏参数
    """
    
    def __init__(self, config: DistillationConfig):
        self.config = config
        self.performance_history = deque(maxlen=config.performance_window)
        self.current_temperature = config.temperature
        self.current_alpha = config.alpha
        
    def update_performance(self, student_accuracy: float, teacher_accuracy: float):
        """
        更新性能记录
        
        Args:
            student_accuracy: 学生模型准确率
            teacher_accuracy: 教师模型准确率
        """
        performance_gap = teacher_accuracy - student_accuracy
        self.performance_history.append({
            'student_accuracy': student_accuracy,
            'teacher_accuracy': teacher_accuracy,
            'performance_gap': performance_gap,
            'timestamp': time.time()
        })
    
    def adapt_parameters(self) -> Tuple[float, float]:
        """
        自适应调整蒸馏参数
        
        Returns:
            (调整后的温度, 调整后的alpha)
        """
        if len(self.performance_history) < 10:
            return self.current_temperature, self.current_alpha
        
        # 计算最近的性能趋势
        recent_gaps = [p['performance_gap'] for p in list(self.performance_history)[-10:]]
        avg_gap = np.mean(recent_gaps)
        gap_trend = np.polyfit(range(len(recent_gaps)), recent_gaps, 1)[0]
        
        # 根据性能差距调整温度
        if avg_gap > 0.1:  # 性能差距较大
            # 增加温度，软化教师输出
            self.current_temperature = min(
                self.current_temperature * 1.1,
                self.config.max_temperature
            )
            # 增加蒸馏权重
            self.current_alpha = min(self.current_alpha * 1.05, 0.9)
        elif avg_gap < 0.02:  # 性能差距较小
            # 降低温度，增强学习信号
            self.current_temperature = max(
                self.current_temperature * 0.95,
                self.config.min_temperature
            )
            # 降低蒸馏权重
            self.current_alpha = max(self.current_alpha * 0.95, 0.1)
        
        # 根据趋势微调
        if gap_trend < -0.01:  # 性能差距在缩小
            self.current_alpha *= 0.98  # 略微降低蒸馏权重
        elif gap_trend > 0.01:  # 性能差距在扩大
            self.current_alpha *= 1.02  # 略微增加蒸馏权重
        
        return self.current_temperature, self.current_alpha
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        if not self.performance_history:
            return {
                'current_temperature': self.current_temperature,
                'current_alpha': self.current_alpha,
                'performance_samples': 0,
                'average_gap': 0.0
            }
        
        recent_gaps = [p['performance_gap'] for p in self.performance_history]
        return {
            'current_temperature': self.current_temperature,
            'current_alpha': self.current_alpha,
            'performance_samples': len(self.performance_history),
            'average_gap': np.mean(recent_gaps),
            'gap_std': np.std(recent_gaps)
        }


class EdgeCloudDistillationFramework:
    """
    端云协同知识蒸馏框架
    
    实现端侧学生模型和云端教师模型之间的知识蒸馏：
    1. 从云端收集高质量的教师知识
    2. 使用自适应策略进行知识蒸馏
    3. 持续优化端侧模型性能
    4. 监控和评估蒸馏效果
    """
    
    def __init__(
        self, 
        student_model: EdgeCFSRec,
        teacher_model: CloudLLMEnhanced,
        config: Optional[DistillationConfig] = None
    ):
        """
        初始化蒸馏框架
        
        Args:
            student_model: 端侧学生模型
            teacher_model: 云端教师模型
            config: 蒸馏配置
        """
        self.student_model = student_model
        self.teacher_model = teacher_model
        self.config = config or DistillationConfig()
        
        # 初始化组件
        self.knowledge_buffer = KnowledgeBuffer(
            max_size=self.config.max_knowledge_buffer_size,
            quality_threshold=self.config.knowledge_quality_threshold
        )
        self.adaptive_strategy = AdaptiveDistillationStrategy(self.config)
        
        # 特征对齐层
        self.feature_aligner = nn.Sequential(
            nn.Linear(self.config.representation_dim, 128),
            nn.ReLU(),
            nn.Linear(128, self.config.representation_dim),
            nn.LayerNorm(self.config.representation_dim)
        )
        
        # 优化器
        self.optimizer = torch.optim.Adam(
            list(self.student_model.parameters()) + list(self.feature_aligner.parameters()),
            lr=self.config.learning_rate
        )
        
        # 损失函数
        self.mse_loss = nn.MSELoss()
        self.kl_div_loss = nn.KLDivLoss(reduction='batchmean')
        
        # 指标记录
        self.metrics_history = []
        self.logger = logging.getLogger(__name__)
        
        # 初始化特征对齐层
        self._initialize_feature_aligner()
    
    def _initialize_feature_aligner(self):
        """初始化特征对齐层"""
        for module in self.feature_aligner:
            if isinstance(module, nn.Linear):
                nn.init.xavier_normal_(module.weight)
                nn.init.zeros_(module.bias)
    
    def collect_teacher_knowledge(
        self, 
        user_sequences: List[List[int]],
        user_ids: List[str]
    ) -> int:
        """
        从云端收集教师知识
        
        Args:
            user_sequences: 用户序列列表
            user_ids: 用户ID列表
            
        Returns:
            收集到的知识数量
        """
        collected_count = 0
        
        for user_seq, user_id in zip(user_sequences, user_ids):
            try:
                # 生成学生模型的用户表示
                student_repr = self.student_model.generate_user_representation(user_seq, user_id)
                
                # 获取教师模型的推荐结果
                teacher_result = self.teacher_model.generate_recommendations(
                    student_repr, user_id, num_recommendations=10
                )
                
                # 评估知识质量
                quality_score = self._evaluate_knowledge_quality(
                    student_repr, teacher_result
                )
                
                # 添加到知识缓冲区
                if quality_score >= self.config.knowledge_quality_threshold:
                    # 将教师输出转换为tensor（简化处理）
                    teacher_output = torch.randn(self.config.representation_dim)  # 占位符
                    
                    self.knowledge_buffer.add_knowledge(
                        user_repr=student_repr,
                        teacher_output=teacher_output,
                        quality_score=quality_score,
                        metadata={
                            'user_id': user_id,
                            'sequence_length': len(user_seq),
                            'num_recommendations': len(teacher_result.get('recommendations', []))
                        }
                    )
                    collected_count += 1
                    
            except Exception as e:
                self.logger.warning(f"收集用户 {user_id} 的教师知识失败: {e}")
        
        self.logger.info(f"成功收集 {collected_count} 条教师知识")
        return collected_count
    
    def _evaluate_knowledge_quality(
        self, 
        student_repr: torch.Tensor,
        teacher_result: Dict[str, Any]
    ) -> float:
        """
        评估知识质量
        
        Args:
            student_repr: 学生表示
            teacher_result: 教师结果
            
        Returns:
            质量分数 (0-1)
        """
        quality_score = 0.0
        
        # 基于推荐数量的质量评估
        recommendations = teacher_result.get('recommendations', [])
        if len(recommendations) >= 5:
            quality_score += 0.3
        
        # 基于响应时间的质量评估
        response_time = teacher_result.get('response_time', float('inf'))
        if response_time < 2.0:  # 2秒内响应
            quality_score += 0.2
        
        # 基于用户表示质量的评估
        repr_norm = torch.norm(student_repr, p=2).item()
        if 1.0 <= repr_norm <= 10.0:  # 合理的表示范数
            quality_score += 0.3
        
        # 基于推荐多样性的评估（简化）
        if len(set(r.get('title', '') for r in recommendations)) >= len(recommendations) * 0.8:
            quality_score += 0.2
        
        return min(quality_score, 1.0)
    
    def distill_knowledge(self, num_epochs: Optional[int] = None) -> List[DistillationMetrics]:
        """
        执行知识蒸馏
        
        Args:
            num_epochs: 蒸馏轮数
            
        Returns:
            蒸馏指标列表
        """
        num_epochs = num_epochs or self.config.distillation_epochs
        epoch_metrics = []
        
        self.logger.info(f"开始知识蒸馏，共 {num_epochs} 轮")
        
        for epoch in range(num_epochs):
            epoch_start_time = time.time()
            
            # 获取当前蒸馏参数
            temperature, alpha = self.adaptive_strategy.adapt_parameters()
            beta = 1.0 - alpha
            
            # 采样知识批次
            knowledge_batch = self.knowledge_buffer.sample_batch(self.config.batch_size)
            
            if not knowledge_batch:
                self.logger.warning(f"第 {epoch + 1} 轮：知识缓冲区为空，跳过")
                continue
            
            # 准备批次数据
            student_reprs = torch.stack([item['user_repr'] for item in knowledge_batch])
            teacher_outputs = torch.stack([item['teacher_output'] for item in knowledge_batch])
            
            # 前向传播
            self.student_model.train()
            self.optimizer.zero_grad()
            
            # 学生模型输出
            student_outputs = self.feature_aligner(student_reprs)
            
            # 计算蒸馏损失
            distillation_loss = self._compute_distillation_loss(
                student_outputs, teacher_outputs, temperature
            )
            
            # 计算任务损失（特征对齐）
            task_loss = self.mse_loss(student_outputs, student_reprs)
            
            # 总损失
            total_loss = alpha * distillation_loss + beta * task_loss
            
            # 反向传播
            total_loss.backward()
            self.optimizer.step()
            
            # 计算准确率（简化）
            student_accuracy = self._compute_accuracy(student_outputs, teacher_outputs)
            teacher_accuracy = 1.0  # 假设教师模型准确率为1
            
            # 更新自适应策略
            self.adaptive_strategy.update_performance(student_accuracy, teacher_accuracy)
            
            # 记录指标
            metrics = DistillationMetrics(
                epoch=epoch + 1,
                distillation_loss=distillation_loss.item(),
                task_loss=task_loss.item(),
                total_loss=total_loss.item(),
                student_accuracy=student_accuracy,
                teacher_accuracy=teacher_accuracy,
                knowledge_transfer_rate=len(knowledge_batch) / max(len(self.knowledge_buffer.buffer), 1),
                timestamp=time.time()
            )
            
            epoch_metrics.append(metrics)
            self.metrics_history.append(metrics)
            
            # 日志输出
            if (epoch + 1) % 5 == 0:
                self.logger.info(
                    f"轮次 {epoch + 1}/{num_epochs}: "
                    f"总损失={total_loss.item():.4f}, "
                    f"蒸馏损失={distillation_loss.item():.4f}, "
                    f"任务损失={task_loss.item():.4f}, "
                    f"学生准确率={student_accuracy:.4f}"
                )
        
        self.logger.info("知识蒸馏完成")
        return epoch_metrics
    
    def _compute_distillation_loss(
        self,
        student_outputs: torch.Tensor,
        teacher_outputs: torch.Tensor,
        temperature: float
    ) -> torch.Tensor:
        """计算双向蒸馏损失"""
        if not self.config.enable_bidirectional:
            # 传统单向蒸馏
            student_soft = F.log_softmax(student_outputs / temperature, dim=-1)
            teacher_soft = F.softmax(teacher_outputs / temperature, dim=-1)
            kl_loss = self.kl_div_loss(student_soft, teacher_soft)
            return kl_loss * (temperature ** 2)

        # 双向蒸馏增强
        # 1. 传统蒸馏：Teacher → Student
        student_soft = F.log_softmax(student_outputs / temperature, dim=-1)
        teacher_soft = F.softmax(teacher_outputs / temperature, dim=-1)
        forward_kl_loss = self.kl_div_loss(student_soft, teacher_soft) * (temperature ** 2)

        # 2. 反向蒸馏：Student → Teacher
        teacher_soft_log = F.log_softmax(teacher_outputs / temperature, dim=-1)
        student_soft_target = F.softmax(student_outputs.detach() / temperature, dim=-1)
        reverse_kl_loss = self.kl_div_loss(teacher_soft_log, student_soft_target) * (temperature ** 2)

        # 3. 互信息最大化损失
        mutual_info_loss = self._compute_mutual_info_loss(student_outputs, teacher_outputs, temperature)

        # 4. 对比学习损失
        contrastive_loss = self._compute_contrastive_loss(student_outputs, teacher_outputs, temperature)

        # 综合双向蒸馏损失
        total_loss = (forward_kl_loss +
                     self.config.reverse_distillation_weight * reverse_kl_loss +
                     self.config.mutual_info_weight * mutual_info_loss +
                     self.config.contrastive_weight * contrastive_loss)

        return total_loss
    
    def _compute_mutual_info_loss(
        self,
        student_outputs: torch.Tensor,
        teacher_outputs: torch.Tensor,
        temperature: float
    ) -> torch.Tensor:
        """
        计算互信息最大化损失

        使用InfoNCE损失来最大化学生和教师表示之间的互信息
        """
        batch_size = student_outputs.size(0)

        # L2归一化
        student_norm = F.normalize(student_outputs, p=2, dim=-1)
        teacher_norm = F.normalize(teacher_outputs, p=2, dim=-1)

        # 计算相似度矩阵
        sim_matrix = torch.mm(student_norm, teacher_norm.t()) / temperature

        # 正样本：对角线元素
        pos_sim = torch.diag(sim_matrix)

        # InfoNCE损失
        labels = torch.arange(batch_size, device=student_outputs.device)
        loss = F.cross_entropy(sim_matrix, labels)

        return loss

    def _compute_contrastive_loss(
        self,
        student_outputs: torch.Tensor,
        teacher_outputs: torch.Tensor,
        temperature: float
    ) -> torch.Tensor:
        """
        计算对比学习损失

        拉近正样本对，推远负样本对
        """
        batch_size = student_outputs.size(0)

        # L2归一化
        student_norm = F.normalize(student_outputs, p=2, dim=-1)
        teacher_norm = F.normalize(teacher_outputs, p=2, dim=-1)

        # 计算余弦相似度矩阵
        sim_matrix = F.cosine_similarity(
            student_norm.unsqueeze(1),
            teacher_norm.unsqueeze(0),
            dim=2
        ) / temperature

        # 创建标签：对角线为正样本
        labels = torch.arange(batch_size, device=student_outputs.device)

        # 对比损失
        loss = F.cross_entropy(sim_matrix, labels)

        return loss

    def _compute_accuracy(
        self,
        student_outputs: torch.Tensor,
        teacher_outputs: torch.Tensor
    ) -> float:
        """计算准确率（简化版本）"""
        # 使用余弦相似度作为准确率指标
        cos_sim = F.cosine_similarity(student_outputs, teacher_outputs, dim=-1)
        return cos_sim.mean().item()
    
    def get_distillation_statistics(self) -> Dict[str, Any]:
        """获取蒸馏统计信息"""
        if not self.metrics_history:
            return {
                'total_epochs': 0,
                'average_loss': 0.0,
                'best_accuracy': 0.0,
                'bidirectional_enabled': self.config.enable_bidirectional,
                'knowledge_buffer_stats': self.knowledge_buffer.get_statistics(),
                'strategy_info': self.adaptive_strategy.get_strategy_info()
            }

        losses = [m.total_loss for m in self.metrics_history]
        accuracies = [m.student_accuracy for m in self.metrics_history]

        stats = {
            'total_epochs': len(self.metrics_history),
            'average_loss': np.mean(losses),
            'best_accuracy': np.max(accuracies),
            'latest_accuracy': accuracies[-1],
            'loss_trend': np.polyfit(range(len(losses)), losses, 1)[0],
            'bidirectional_enabled': self.config.enable_bidirectional,
            'knowledge_buffer_stats': self.knowledge_buffer.get_statistics(),
            'strategy_info': self.adaptive_strategy.get_strategy_info()
        }

        # 双向蒸馏特有统计
        if self.config.enable_bidirectional:
            stats.update({
                'reverse_distillation_weight': self.config.reverse_distillation_weight,
                'mutual_info_weight': self.config.mutual_info_weight,
                'contrastive_weight': self.config.contrastive_weight,
                'distillation_mode': 'bidirectional'
            })
        else:
            stats['distillation_mode'] = 'unidirectional'

        return stats
    
    def save_framework(self, save_path: str):
        """保存蒸馏框架"""
        save_dict = {
            'feature_aligner': self.feature_aligner.state_dict(),
            'optimizer': self.optimizer.state_dict(),
            'config': self.config,
            'metrics_history': self.metrics_history,
            'adaptive_strategy_state': {
                'current_temperature': self.adaptive_strategy.current_temperature,
                'current_alpha': self.adaptive_strategy.current_alpha,
                'performance_history': list(self.adaptive_strategy.performance_history)
            }
        }
        
        torch.save(save_dict, save_path)
        self.logger.info(f"蒸馏框架已保存到 {save_path}")
    
    def load_framework(self, load_path: str):
        """加载蒸馏框架"""
        checkpoint = torch.load(load_path, map_location='cpu')
        
        self.feature_aligner.load_state_dict(checkpoint['feature_aligner'])
        self.optimizer.load_state_dict(checkpoint['optimizer'])
        
        if 'metrics_history' in checkpoint:
            self.metrics_history = checkpoint['metrics_history']
        
        if 'adaptive_strategy_state' in checkpoint:
            state = checkpoint['adaptive_strategy_state']
            self.adaptive_strategy.current_temperature = state['current_temperature']
            self.adaptive_strategy.current_alpha = state['current_alpha']
            self.adaptive_strategy.performance_history = deque(
                state['performance_history'], 
                maxlen=self.config.performance_window
            )
        
        self.logger.info(f"蒸馏框架已从 {load_path} 加载")


def create_distillation_framework(
    student_model: EdgeCFSRec,
    teacher_model: CloudLLMEnhanced,
    distillation_config: Optional[DistillationConfig] = None,
    enable_bidirectional: bool = True
) -> EdgeCloudDistillationFramework:
    """
    创建端云协同知识蒸馏框架的便捷函数

    Args:
        student_model: 端侧学生模型
        teacher_model: 云端教师模型
        distillation_config: 蒸馏配置
        enable_bidirectional: 是否启用双向蒸馏

    Returns:
        初始化后的蒸馏框架
    """
    if distillation_config is None:
        distillation_config = DistillationConfig(
            temperature=4.0,
            alpha=0.7,
            distillation_epochs=10,
            learning_rate=1e-4,
            enable_bidirectional=enable_bidirectional,
            reverse_distillation_weight=0.3,
            mutual_info_weight=0.2,
            contrastive_weight=0.1
        )

    framework = EdgeCloudDistillationFramework(
        student_model, teacher_model, distillation_config
    )

    return framework


def create_bidirectional_distillation_framework(
    student_model: EdgeCFSRec,
    teacher_model: CloudLLMEnhanced,
    **kwargs
) -> EdgeCloudDistillationFramework:
    """
    创建双向知识蒸馏框架的便捷函数

    Args:
        student_model: 端侧学生模型
        teacher_model: 云端教师模型
        **kwargs: 其他配置参数

    Returns:
        配置了双向蒸馏的框架
    """
    config = DistillationConfig(
        enable_bidirectional=True,
        temperature=kwargs.get('temperature', 4.0),
        alpha=kwargs.get('alpha', 0.7),
        reverse_distillation_weight=kwargs.get('reverse_weight', 0.3),
        mutual_info_weight=kwargs.get('mutual_info_weight', 0.2),
        contrastive_weight=kwargs.get('contrastive_weight', 0.1),
        distillation_epochs=kwargs.get('epochs', 10),
        learning_rate=kwargs.get('lr', 1e-4)
    )

    return EdgeCloudDistillationFramework(student_model, teacher_model, config)
