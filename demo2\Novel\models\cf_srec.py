"""
CF-SRec: 协同过滤序列推荐模型

基于SASRec的轻量级端侧推荐模型，用于序列建模和协同过滤。
在Novel端云协同推荐系统中作为端侧模型使用。
"""

import numpy as np
import torch
import torch.nn as nn
from typing import Dict, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class PointWiseFeedForward(nn.Module):
    """
    逐点前馈网络 (Point-wise Feed-Forward Network)
    
    SASRec模型中Transformer块的前馈网络组件
    使用两个1D卷积层实现逐点的非线性变换
    """
    
    def __init__(self, hidden_units: int, dropout_rate: float):
        super(PointWiseFeedForward, self).__init__()
        
        # 第一个1D卷积层：hidden_units -> hidden_units
        self.conv1 = nn.Conv1d(hidden_units, hidden_units, kernel_size=1)
        self.dropout1 = nn.Dropout(p=dropout_rate)
        self.relu = nn.ReLU()
        
        # 第二个1D卷积层：hidden_units -> hidden_units
        self.conv2 = nn.Conv1d(hidden_units, hidden_units, kernel_size=1)
        self.dropout2 = nn.Dropout(p=dropout_rate)
    
    def forward(self, inputs):
        """前向传播"""
        # 转置以适应Conv1d的输入格式 [batch_size, hidden_units, seq_len]
        outputs = self.dropout2(self.conv2(self.relu(self.dropout1(self.conv1(inputs.transpose(-1, -2))))))
        
        # 转置回原始格式 [batch_size, seq_len, hidden_units]
        outputs = outputs.transpose(-1, -2)
        
        # 残差连接：输出 = 变换后的特征 + 原始输入
        outputs += inputs
        return outputs


class CFSRec(nn.Module):
    """
    CF-SRec (Collaborative Filtering Sequential Recommendation) 模型
    
    基于SASRec的端侧序列推荐模型，结合协同过滤和序列建模。
    在Novel端云协同推荐系统中作为轻量级端侧模型使用。
    """
    
    def __init__(self, config: Dict):
        """
        初始化CF-SRec模型
        
        Args:
            config: 模型配置字典
        """
        super(CFSRec, self).__init__()

        self.config = config
        self.item_num = config.get('item_num', 10000)
        self.hidden_size = config.get('hidden_size', 64)
        self.max_seq_length = config.get('max_seq_length', 50)
        self.num_attention_heads = config.get('num_attention_heads', 2)
        self.num_hidden_layers = config.get('num_hidden_layers', 2)
        self.dropout_prob = config.get('dropout_prob', 0.1)
        self.user_repr_dim = config.get('user_repr_dim', 64)

        # 注册一个buffer来跟踪设备，避免多GPU问题
        self.register_buffer('_device_tracker', torch.zeros(1))
        
        # 物品嵌入层
        self.item_emb = nn.Embedding(self.item_num + 1, self.hidden_size, padding_idx=0)
        self.item_emb.weight.data.normal_(0.0, 1)
        
        # 位置嵌入层
        self.pos_emb = nn.Embedding(self.max_seq_length, self.hidden_size)
        
        # 嵌入层Dropout
        self.emb_dropout = nn.Dropout(p=self.dropout_prob)
        
        # Transformer组件
        self.attention_layernorms = nn.ModuleList()
        self.attention_layers = nn.ModuleList()
        self.forward_layernorms = nn.ModuleList()
        self.forward_layers = nn.ModuleList()
        
        # 最终LayerNorm
        self.last_layernorm = nn.LayerNorm(self.hidden_size, eps=1e-8)
        
        # 用户表示生成层
        self.user_repr_layer = nn.Linear(self.hidden_size, self.user_repr_dim)
        
        # 构建多层Transformer
        for _ in range(self.num_hidden_layers):
            # 注意力层
            self.attention_layernorms.append(nn.LayerNorm(self.hidden_size, eps=1e-8))
            self.attention_layers.append(nn.MultiheadAttention(
                self.hidden_size, self.num_attention_heads, self.dropout_prob
            ))
            
            # 前馈层
            self.forward_layernorms.append(nn.LayerNorm(self.hidden_size, eps=1e-8))
            self.forward_layers.append(PointWiseFeedForward(self.hidden_size, self.dropout_prob))
        
        logger.info(f"Initialized CFSRec with {sum(p.numel() for p in self.parameters())} parameters")

    @property
    def device(self):
        """获取模型设备，兼容多GPU"""
        return self._device_tracker.device

    def log2feats(self, log_seqs):
        """
        将用户行为序列转换为特征表示
        
        Args:
            log_seqs: 用户行为序列，形状为 [batch_size, seq_len]
            
        Returns:
            序列特征表示，形状为 [batch_size, seq_len, hidden_size]
        """
        # 物品嵌入
        if isinstance(log_seqs, torch.Tensor):
            seqs = self.item_emb(log_seqs.long().to(self.device))
        else:
            seqs = self.item_emb(torch.LongTensor(log_seqs).to(self.device))
        seqs *= self.item_emb.embedding_dim ** 0.5

        # 位置嵌入
        if isinstance(log_seqs, torch.Tensor):
            seq_shape = log_seqs.shape
        else:
            seq_shape = np.array(log_seqs).shape

        # 确保序列长度匹配模型配置
        actual_seq_len = seq_shape[1]
        model_seq_len = self.max_seq_length

        if actual_seq_len != model_seq_len:
            if actual_seq_len > model_seq_len:
                # 截取到模型长度
                if isinstance(log_seqs, torch.Tensor):
                    log_seqs = log_seqs[:, :model_seq_len]
                else:
                    log_seqs = log_seqs[:, :model_seq_len]
                seqs = seqs[:, :model_seq_len, :]
                seq_len = model_seq_len
            else:
                # 填充到模型长度
                pad_len = model_seq_len - actual_seq_len
                if isinstance(log_seqs, torch.Tensor):
                    pad_tensor = torch.zeros(seq_shape[0], pad_len, dtype=log_seqs.dtype, device=log_seqs.device)
                    log_seqs = torch.cat([pad_tensor, log_seqs], dim=1)

                pad_emb = torch.zeros(seq_shape[0], pad_len, self.hidden_size, device=seqs.device)
                seqs = torch.cat([pad_emb, seqs], dim=1)
                seq_len = model_seq_len
        else:
            seq_len = actual_seq_len

        # 位置嵌入
        positions = np.tile(np.array(range(seq_len)), [seq_shape[0], 1])
        seqs += self.pos_emb(torch.LongTensor(positions).to(self.device))

        # Dropout
        seqs = self.emb_dropout(seqs)

        # 创建掩码（确保长度匹配）
        if isinstance(log_seqs, torch.Tensor):
            timeline_mask = (log_seqs == 0).bool().to(self.device)
        else:
            timeline_mask = torch.BoolTensor(log_seqs == 0).to(self.device)

        # 确保掩码长度与序列长度匹配
        if timeline_mask.shape[1] != seqs.shape[1]:
            if timeline_mask.shape[1] > seqs.shape[1]:
                timeline_mask = timeline_mask[:, :seqs.shape[1]]
            else:
                pad_len = seqs.shape[1] - timeline_mask.shape[1]
                pad_mask = torch.ones(timeline_mask.shape[0], pad_len, dtype=torch.bool, device=timeline_mask.device)
                timeline_mask = torch.cat([pad_mask, timeline_mask], dim=1)

        seqs *= ~timeline_mask.unsqueeze(-1)

        # 因果注意力掩码
        tl = seqs.shape[1]
        attention_mask = ~torch.tril(torch.ones((tl, tl), dtype=torch.bool, device=self.device))
        
        # 多层Transformer编码
        for i in range(len(self.attention_layers)):
            seqs = torch.transpose(seqs, 0, 1)  # [seq_len, batch_size, hidden_size]
            
            # 自注意力
            Q = self.attention_layernorms[i](seqs)
            mha_outputs, _ = self.attention_layers[i](Q, seqs, seqs, attn_mask=attention_mask)
            seqs = Q + mha_outputs
            
            seqs = torch.transpose(seqs, 0, 1)  # [batch_size, seq_len, hidden_size]
            
            # 前馈网络
            seqs = self.forward_layernorms[i](seqs)
            seqs = self.forward_layers[i](seqs)
            
            # 重新应用掩码
            seqs *= ~timeline_mask.unsqueeze(-1)
        
        # 最终LayerNorm
        log_feats = self.last_layernorm(seqs)
        return log_feats
    
    def forward(self, input_ids: torch.Tensor, attention_mask: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            input_ids: 输入序列 [batch_size, seq_length]
            attention_mask: 注意力掩码 [batch_size, seq_length]
            
        Returns:
            模型输出字典
        """
        # 转换为numpy进行处理（兼容原始SASRec接口）
        if isinstance(input_ids, torch.Tensor):
            log_seqs = input_ids.cpu().numpy()
        else:
            log_seqs = input_ids
        
        # 获取序列特征
        log_feats = self.log2feats(log_seqs)  # [batch_size, seq_len, hidden_size]
        
        # 获取序列表示（最后一个非padding位置）
        batch_size = log_feats.shape[0]
        if attention_mask is not None:
            sequence_lengths = attention_mask.sum(dim=1) - 1
        else:
            # 找到最后一个非零位置
            mask = (torch.tensor(log_seqs) != 0)
            sequence_lengths = mask.sum(dim=1) - 1
        
        batch_indices = torch.arange(batch_size, device=log_feats.device)
        sequence_output = log_feats[batch_indices, sequence_lengths]  # [batch_size, hidden_size]
        
        # 生成用户表示
        user_repr = self.user_repr_layer(sequence_output)  # [batch_size, user_repr_dim]
        
        # 生成预测logits
        all_item_embs = self.item_emb.weight[1:]  # 排除padding
        prediction_logits = torch.matmul(sequence_output, all_item_embs.T)
        
        return {
            'prediction_logits': prediction_logits,
            'user_repr': user_repr,
            'hidden_states': log_feats,
            'sequence_output': sequence_output
        }
    
    def recommend(self, 
                  user_history: List[int], 
                  top_k: int = 10,
                  exclude_seen: bool = True) -> List[int]:
        """
        为用户生成推荐
        
        Args:
            user_history: 用户历史序列
            top_k: 推荐数量
            exclude_seen: 是否排除已见过的物品
            
        Returns:
            推荐物品列表
        """
        self.eval()
        
        with torch.no_grad():
            # 准备输入
            if len(user_history) > self.max_seq_length:
                user_history = user_history[-self.max_seq_length:]
            
            # 填充到固定长度
            padded_history = [0] * (self.max_seq_length - len(user_history)) + user_history
            input_ids = torch.tensor([padded_history], dtype=torch.long)
            
            # 前向传播
            outputs = self.forward(input_ids)
            logits = outputs['prediction_logits'].squeeze(0)  # [item_num]
            
            # 排除已见过的物品
            if exclude_seen:
                for item_id in user_history:
                    if 0 < item_id < len(logits):
                        logits[item_id - 1] = float('-inf')  # -1因为排除了padding
            
            # 获取top-k推荐
            _, top_indices = torch.topk(logits, top_k)
            recommendations = (top_indices + 1).tolist()  # +1恢复原始item_id
            
            return recommendations
    
    def get_user_representation(self, user_history: List[int]) -> torch.Tensor:
        """
        获取用户表示
        
        Args:
            user_history: 用户历史序列
            
        Returns:
            用户表示向量
        """
        self.eval()
        
        with torch.no_grad():
            # 准备输入
            if len(user_history) > self.max_seq_length:
                user_history = user_history[-self.max_seq_length:]
            
            # 填充到固定长度
            padded_history = [0] * (self.max_seq_length - len(user_history)) + user_history
            input_ids = torch.tensor([padded_history], dtype=torch.long)
            
            # 前向传播
            outputs = self.forward(input_ids)
            user_repr = outputs['user_repr'].squeeze(0)  # [user_repr_dim]
            
            return user_repr
