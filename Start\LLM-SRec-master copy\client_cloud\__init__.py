"""
端云协同推荐系统核心模块

本包包含端云协同推荐系统的核心组件：
- EdgeCFSRec: 端侧CF-SRec模型
- CloudLLMEnhanced: 云端LLM增强模型  
- EdgeCloudCommunicator: 端云通信器
- EdgeCloudRecommendationSystem: 主系统集成
- EdgeCloudDistillationFramework: 知识蒸馏框架

使用示例:
    from client_cloud import EdgeCloudRecommendationSystem, SystemConfig
    
    system = EdgeCloudRecommendationSystem(SystemConfig())
    await system.initialize()
"""

# 导入核心类和函数
from .edge_cf_srec import EdgeCFSRecAdapter, EdgeCFSRec, create_edge_cf_srec
from .cloud_llm_enhanced import CloudLLMEnhanced, create_cloud_llm_enhanced
from .edge_cloud_communication import (
    EdgeCloudCommunicator, 
    CommunicationConfig,
    create_communicator
)
from .edge_cloud_system import (
    EdgeCloudRecommendationSystem,
    SystemConfig,
    SystemMetrics,
    RecommendationMode,
    create_edge_cloud_system
)
from .knowledge_distillation import (
    EdgeCloudDistillationFramework,
    DistillationConfig,
    DistillationMetrics,
    create_distillation_framework
)

# 定义公开的API
__all__ = [
    # 核心系统类
    'EdgeCloudRecommendationSystem',
    'EdgeCFSRecAdapter',
    'EdgeCFSRec',  # 向后兼容别名
    'CloudLLMEnhanced',
    'EdgeCloudCommunicator',
    'EdgeCloudDistillationFramework',
    
    # 配置类
    'SystemConfig',
    'CommunicationConfig', 
    'DistillationConfig',
    
    # 数据类
    'SystemMetrics',
    'DistillationMetrics',
    
    # 枚举类
    'RecommendationMode',
    
    # 便捷函数
    'create_edge_cloud_system',
    'create_edge_cf_srec',
    'create_cloud_llm_enhanced', 
    'create_communicator',
    'create_distillation_framework',
]

# 版本信息
__version__ = '1.0.0'
__author__ = 'Edge-Cloud Recommendation Team'
__description__ = 'Edge-Cloud Collaborative Recommendation System'
