"""
隐私保护模块

本包提供端云协同推荐系统的隐私保护功能：
- 差分隐私保护：拉普拉斯机制和高斯机制
- 安全通信：端到端加密和消息认证
- 隐私风险评估：动态评估和调整隐私保护级别
- 综合隐私保护：整合所有隐私保护组件

使用示例:
    from privacy import EdgeCloudPrivacyProtector, PrivacyConfig
    
    config = PrivacyConfig(privacy_level='high')
    protector = EdgeCloudPrivacyProtector(config)
    encrypted_package, risk_assessment = protector.protect_and_transmit(user_repr, user_id)
"""

# 导入核心隐私保护组件
from .privacy_protection import (
    PrivacyConfig,
    DifferentialPrivacyProtector,
    SecureCommunicator,
    PrivacyRiskAssessor,
    EdgeCloudPrivacyProtector
)

# 导入差分隐私子模块
from .differential_privacy import (
    LaplaceNoiseMechanism,
    GaussianNoiseMechanism,
    PrivacyBudgetManager
)

# 导入加密通信子模块
from .secure_communication import (
    EncryptionManager,
    MessageAuthenticator,
    KeyRotationManager
)

# 导入风险评估子模块
from .risk_assessment import (
    PrivacyRiskAnalyzer,
    RiskMetrics,
    AdaptivePrivacyController
)

# 定义公开的API
__all__ = [
    # 主要配置和保护器
    'PrivacyConfig',
    'EdgeCloudPrivacyProtector',
    
    # 核心组件
    'DifferentialPrivacyProtector',
    'SecureCommunicator', 
    'PrivacyRiskAssessor',
    
    # 差分隐私组件
    'LaplaceNoiseMechanism',
    'GaussianNoiseMechanism',
    'PrivacyBudgetManager',
    
    # 安全通信组件
    'EncryptionManager',
    'MessageAuthenticator',
    'KeyRotationManager',
    
    # 风险评估组件
    'PrivacyRiskAnalyzer',
    'RiskMetrics',
    'AdaptivePrivacyController',
]

# 版本信息
__version__ = '1.0.0'
__author__ = 'Privacy Protection Team'
__description__ = 'Privacy Protection Module for Edge-Cloud Recommendation System'

# 隐私保护级别常量
PRIVACY_LEVELS = {
    'low': {
        'epsilon': 4.0,
        'delta': 1e-3,
        'noise_multiplier': 0.5,
        'description': '低隐私保护级别，适用于对隐私要求不高的场景'
    },
    'medium': {
        'epsilon': 2.0,
        'delta': 1e-4,
        'noise_multiplier': 1.0,
        'description': '中等隐私保护级别，平衡隐私和效用'
    },
    'high': {
        'epsilon': 1.0,
        'delta': 1e-5,
        'noise_multiplier': 2.0,
        'description': '高隐私保护级别，提供强隐私保证'
    },
    'ultra': {
        'epsilon': 0.5,
        'delta': 1e-6,
        'noise_multiplier': 4.0,
        'description': '超高隐私保护级别，最大化隐私保护'
    }
}

def get_privacy_level_config(level: str) -> dict:
    """
    获取隐私保护级别配置
    
    Args:
        level: 隐私保护级别 ('low', 'medium', 'high', 'ultra')
        
    Returns:
        隐私保护配置字典
    """
    if level not in PRIVACY_LEVELS:
        raise ValueError(f"不支持的隐私保护级别: {level}. 支持的级别: {list(PRIVACY_LEVELS.keys())}")
    
    return PRIVACY_LEVELS[level].copy()


def create_privacy_protector(
    privacy_level: str = 'high',
    user_repr_dim: int = 64,
    enable_encryption: bool = True,
    **kwargs
) -> EdgeCloudPrivacyProtector:
    """
    创建隐私保护器的便捷函数
    
    Args:
        privacy_level: 隐私保护级别
        user_repr_dim: 用户表示维度
        enable_encryption: 是否启用加密
        **kwargs: 其他配置参数
        
    Returns:
        配置好的隐私保护器
    """
    # 获取隐私级别配置
    level_config = get_privacy_level_config(privacy_level)
    
    # 创建隐私配置
    config = PrivacyConfig(
        epsilon=level_config['epsilon'],
        delta=level_config['delta'],
        user_repr_dim=user_repr_dim,
        privacy_level=privacy_level,
        enable_encryption=enable_encryption,
        **kwargs
    )
    
    return EdgeCloudPrivacyProtector(config)


# 隐私保护最佳实践指南
PRIVACY_BEST_PRACTICES = {
    'data_minimization': '只收集和处理必要的数据',
    'purpose_limitation': '数据只用于指定的目的',
    'storage_limitation': '数据保存时间最小化',
    'accuracy': '确保数据的准确性和及时更新',
    'security': '采用适当的技术和组织措施保护数据',
    'transparency': '向用户明确说明数据处理方式',
    'user_control': '给予用户对其数据的控制权',
    'accountability': '能够证明符合隐私保护要求'
}

def get_privacy_best_practices() -> dict:
    """
    获取隐私保护最佳实践指南
    
    Returns:
        隐私保护最佳实践字典
    """
    return PRIVACY_BEST_PRACTICES.copy()


# 隐私保护合规性检查
def check_privacy_compliance(config: PrivacyConfig) -> dict:
    """
    检查隐私保护配置的合规性
    
    Args:
        config: 隐私保护配置
        
    Returns:
        合规性检查结果
    """
    compliance_result = {
        'compliant': True,
        'warnings': [],
        'recommendations': []
    }
    
    # 检查差分隐私参数
    if config.epsilon > 4.0:
        compliance_result['warnings'].append('隐私预算epsilon过大，可能影响隐私保护效果')
        compliance_result['recommendations'].append('建议将epsilon设置为4.0以下')
    
    if config.delta > 1e-3:
        compliance_result['warnings'].append('失败概率delta过大，可能影响隐私保护效果')
        compliance_result['recommendations'].append('建议将delta设置为1e-3以下')
    
    # 检查加密设置
    if not config.enable_encryption:
        compliance_result['warnings'].append('未启用加密传输，存在数据泄露风险')
        compliance_result['recommendations'].append('建议启用加密传输功能')
    
    # 检查用户表示维度
    if config.user_repr_dim > 128:
        compliance_result['warnings'].append('用户表示维度过高，可能增加隐私泄露风险')
        compliance_result['recommendations'].append('建议将用户表示维度控制在128以下')
    
    # 如果有警告，标记为不完全合规
    if compliance_result['warnings']:
        compliance_result['compliant'] = False
    
    return compliance_result
