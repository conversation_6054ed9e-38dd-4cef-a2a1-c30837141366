"""
差分隐私保护子模块

本模块提供专门的差分隐私保护机制：
1. 拉普拉斯噪声机制：实现ε-差分隐私
2. 高斯噪声机制：实现(ε,δ)-差分隐私
3. 隐私预算管理：跟踪和管理隐私预算消耗
4. 自适应噪声调整：根据数据特性动态调整噪声

核心功能：
- 多种噪声机制：支持不同的差分隐私算法
- 隐私预算跟踪：防止隐私预算过度消耗
- 噪声校准：根据敏感度和隐私参数计算最优噪声
- 组合定理：支持多次查询的隐私保证
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
import logging
import time
from abc import ABC, abstractmethod


@dataclass
class PrivacyParameters:
    """差分隐私参数"""
    epsilon: float  # 隐私预算
    delta: float = 0.0  # 失败概率（用于(ε,δ)-差分隐私）
    sensitivity: float = 1.0  # 全局敏感度
    clipping_bound: float = 1.0  # 梯度裁剪界限


class NoiseMechanism(ABC):
    """噪声机制抽象基类"""
    
    def __init__(self, privacy_params: PrivacyParameters):
        self.privacy_params = privacy_params
        self.logger = logging.getLogger(__name__)
    
    @abstractmethod
    def add_noise(self, data: torch.Tensor) -> torch.Tensor:
        """添加噪声到数据"""
        pass
    
    @abstractmethod
    def get_noise_scale(self) -> float:
        """获取噪声尺度"""
        pass


class LaplaceNoiseMechanism(NoiseMechanism):
    """
    拉普拉斯噪声机制
    
    实现ε-差分隐私，适用于数值查询
    噪声尺度 = 敏感度 / ε
    """
    
    def __init__(self, privacy_params: PrivacyParameters):
        super().__init__(privacy_params)
        self.noise_scale = self.get_noise_scale()
    
    def get_noise_scale(self) -> float:
        """计算拉普拉斯噪声尺度"""
        return self.privacy_params.sensitivity / self.privacy_params.epsilon
    
    def add_noise(self, data: torch.Tensor) -> torch.Tensor:
        """
        添加拉普拉斯噪声
        
        Args:
            data: 原始数据张量
            
        Returns:
            添加噪声后的数据
        """
        # 生成拉普拉斯噪声
        noise = torch.from_numpy(
            np.random.laplace(0, self.noise_scale, data.shape)
        ).float().to(data.device)
        
        return data + noise
    
    def get_privacy_cost(self) -> Tuple[float, float]:
        """获取隐私成本"""
        return self.privacy_params.epsilon, 0.0


class GaussianNoiseMechanism(NoiseMechanism):
    """
    高斯噪声机制
    
    实现(ε,δ)-差分隐私，适用于梯度扰动
    噪声尺度 = σ = c * 敏感度 / ε，其中c由δ确定
    """
    
    def __init__(self, privacy_params: PrivacyParameters):
        super().__init__(privacy_params)
        self.noise_scale = self.get_noise_scale()
    
    def get_noise_scale(self) -> float:
        """计算高斯噪声尺度"""
        if self.privacy_params.delta <= 0:
            raise ValueError("高斯机制需要δ > 0")
        
        # 根据(ε,δ)-差分隐私理论计算噪声尺度
        c = np.sqrt(2 * np.log(1.25 / self.privacy_params.delta))
        return c * self.privacy_params.sensitivity / self.privacy_params.epsilon
    
    def add_noise(self, data: torch.Tensor) -> torch.Tensor:
        """
        添加高斯噪声
        
        Args:
            data: 原始数据张量
            
        Returns:
            添加噪声后的数据
        """
        # 生成高斯噪声
        noise = torch.randn_like(data) * self.noise_scale
        
        return data + noise
    
    def get_privacy_cost(self) -> Tuple[float, float]:
        """获取隐私成本"""
        return self.privacy_params.epsilon, self.privacy_params.delta


class ExponentialNoiseMechanism(NoiseMechanism):
    """
    指数噪声机制
    
    适用于选择查询，如Top-k选择
    """
    
    def __init__(self, privacy_params: PrivacyParameters):
        super().__init__(privacy_params)
        self.noise_scale = self.get_noise_scale()
    
    def get_noise_scale(self) -> float:
        """计算指数噪声尺度"""
        return self.privacy_params.epsilon / (2 * self.privacy_params.sensitivity)
    
    def add_noise(self, scores: torch.Tensor) -> torch.Tensor:
        """
        添加指数噪声（用于选择机制）
        
        Args:
            scores: 候选项分数
            
        Returns:
            添加噪声后的分数
        """
        # 生成指数分布噪声
        noise = torch.from_numpy(
            np.random.exponential(1.0 / self.noise_scale, scores.shape)
        ).float().to(scores.device)
        
        return scores + noise
    
    def get_privacy_cost(self) -> Tuple[float, float]:
        """获取隐私成本"""
        return self.privacy_params.epsilon, 0.0


class PrivacyBudgetManager:
    """
    隐私预算管理器
    
    跟踪和管理隐私预算的消耗，防止隐私泄露
    """
    
    def __init__(self, total_epsilon: float, total_delta: float = 0.0):
        self.total_epsilon = total_epsilon
        self.total_delta = total_delta
        self.consumed_epsilon = 0.0
        self.consumed_delta = 0.0
        self.query_history = []
        self.logger = logging.getLogger(__name__)
    
    def check_budget_availability(self, epsilon: float, delta: float = 0.0) -> bool:
        """
        检查隐私预算是否足够
        
        Args:
            epsilon: 请求的ε预算
            delta: 请求的δ预算
            
        Returns:
            是否有足够的预算
        """
        return (self.consumed_epsilon + epsilon <= self.total_epsilon and 
                self.consumed_delta + delta <= self.total_delta)
    
    def consume_budget(self, epsilon: float, delta: float = 0.0, query_info: Optional[Dict] = None):
        """
        消耗隐私预算
        
        Args:
            epsilon: 消耗的ε预算
            delta: 消耗的δ预算
            query_info: 查询信息
        """
        if not self.check_budget_availability(epsilon, delta):
            raise ValueError(f"隐私预算不足: 需要(ε={epsilon}, δ={delta}), "
                           f"剩余(ε={self.total_epsilon - self.consumed_epsilon}, "
                           f"δ={self.total_delta - self.consumed_delta})")
        
        self.consumed_epsilon += epsilon
        self.consumed_delta += delta
        
        # 记录查询历史
        query_record = {
            'timestamp': time.time(),
            'epsilon': epsilon,
            'delta': delta,
            'info': query_info or {}
        }
        self.query_history.append(query_record)
        
        self.logger.info(f"消耗隐私预算: ε={epsilon}, δ={delta}. "
                        f"剩余: ε={self.total_epsilon - self.consumed_epsilon}, "
                        f"δ={self.total_delta - self.consumed_delta}")
    
    def get_remaining_budget(self) -> Tuple[float, float]:
        """获取剩余隐私预算"""
        return (self.total_epsilon - self.consumed_epsilon, 
                self.total_delta - self.consumed_delta)
    
    def get_budget_statistics(self) -> Dict[str, Any]:
        """获取预算统计信息"""
        return {
            'total_epsilon': self.total_epsilon,
            'total_delta': self.total_delta,
            'consumed_epsilon': self.consumed_epsilon,
            'consumed_delta': self.consumed_delta,
            'remaining_epsilon': self.total_epsilon - self.consumed_epsilon,
            'remaining_delta': self.total_delta - self.consumed_delta,
            'utilization_rate': self.consumed_epsilon / self.total_epsilon,
            'query_count': len(self.query_history)
        }
    
    def reset_budget(self):
        """重置隐私预算"""
        self.consumed_epsilon = 0.0
        self.consumed_delta = 0.0
        self.query_history.clear()
        self.logger.info("隐私预算已重置")


class AdaptiveNoiseController:
    """
    自适应噪声控制器
    
    根据数据特性和隐私需求动态调整噪声参数
    """
    
    def __init__(self, base_privacy_params: PrivacyParameters):
        self.base_params = base_privacy_params
        self.adaptation_history = []
        self.logger = logging.getLogger(__name__)
    
    def adapt_noise_parameters(
        self, 
        data: torch.Tensor,
        privacy_level: str = 'medium'
    ) -> PrivacyParameters:
        """
        自适应调整噪声参数
        
        Args:
            data: 输入数据
            privacy_level: 隐私保护级别
            
        Returns:
            调整后的隐私参数
        """
        # 分析数据特性
        data_stats = self._analyze_data_characteristics(data)
        
        # 根据隐私级别调整参数
        adapted_params = self._adjust_parameters_by_level(privacy_level, data_stats)
        
        # 记录调整历史
        adaptation_record = {
            'timestamp': time.time(),
            'original_params': self.base_params,
            'adapted_params': adapted_params,
            'data_stats': data_stats,
            'privacy_level': privacy_level
        }
        self.adaptation_history.append(adaptation_record)
        
        return adapted_params
    
    def _analyze_data_characteristics(self, data: torch.Tensor) -> Dict[str, float]:
        """分析数据特性"""
        return {
            'mean': torch.mean(data).item(),
            'std': torch.std(data).item(),
            'norm': torch.norm(data, p=2).item(),
            'sparsity': (data == 0).float().mean().item(),
            'max_value': torch.max(torch.abs(data)).item()
        }
    
    def _adjust_parameters_by_level(
        self, 
        privacy_level: str, 
        data_stats: Dict[str, float]
    ) -> PrivacyParameters:
        """根据隐私级别调整参数"""
        # 基础参数
        epsilon = self.base_params.epsilon
        delta = self.base_params.delta
        sensitivity = self.base_params.sensitivity
        
        # 根据隐私级别调整
        if privacy_level == 'low':
            epsilon *= 2.0  # 降低隐私保护，减少噪声
        elif privacy_level == 'high':
            epsilon *= 0.5  # 提高隐私保护，增加噪声
        elif privacy_level == 'ultra':
            epsilon *= 0.25  # 超高隐私保护
        
        # 根据数据特性微调
        if data_stats['sparsity'] > 0.5:  # 稀疏数据
            sensitivity *= 0.8  # 降低敏感度
        
        if data_stats['norm'] > 10.0:  # 高范数数据
            sensitivity *= 1.2  # 提高敏感度
        
        return PrivacyParameters(
            epsilon=epsilon,
            delta=delta,
            sensitivity=sensitivity,
            clipping_bound=min(data_stats['max_value'], self.base_params.clipping_bound)
        )
    
    def get_adaptation_statistics(self) -> Dict[str, Any]:
        """获取自适应统计信息"""
        if not self.adaptation_history:
            return {'adaptation_count': 0}
        
        # 计算平均调整幅度
        epsilon_adjustments = [
            record['adapted_params'].epsilon / record['original_params'].epsilon
            for record in self.adaptation_history
        ]
        
        return {
            'adaptation_count': len(self.adaptation_history),
            'average_epsilon_adjustment': np.mean(epsilon_adjustments),
            'epsilon_adjustment_std': np.std(epsilon_adjustments),
            'latest_adaptation': self.adaptation_history[-1] if self.adaptation_history else None
        }


def create_noise_mechanism(
    mechanism_type: str,
    privacy_params: PrivacyParameters
) -> NoiseMechanism:
    """
    创建噪声机制的工厂函数
    
    Args:
        mechanism_type: 噪声机制类型 ('laplace', 'gaussian', 'exponential')
        privacy_params: 隐私参数
        
    Returns:
        对应的噪声机制实例
    """
    if mechanism_type == 'laplace':
        return LaplaceNoiseMechanism(privacy_params)
    elif mechanism_type == 'gaussian':
        return GaussianNoiseMechanism(privacy_params)
    elif mechanism_type == 'exponential':
        return ExponentialNoiseMechanism(privacy_params)
    else:
        raise ValueError(f"不支持的噪声机制类型: {mechanism_type}")


def calculate_composition_privacy(
    privacy_costs: List[Tuple[float, float]],
    composition_type: str = 'basic'
) -> Tuple[float, float]:
    """
    计算组合查询的隐私成本
    
    Args:
        privacy_costs: 各次查询的隐私成本 [(ε1, δ1), (ε2, δ2), ...]
        composition_type: 组合类型 ('basic', 'advanced')
        
    Returns:
        总的隐私成本 (ε_total, δ_total)
    """
    if composition_type == 'basic':
        # 基础组合：简单相加
        total_epsilon = sum(cost[0] for cost in privacy_costs)
        total_delta = sum(cost[1] for cost in privacy_costs)
    elif composition_type == 'advanced':
        # 高级组合：使用更紧的界限
        epsilons = [cost[0] for cost in privacy_costs]
        deltas = [cost[1] for cost in privacy_costs]
        
        # 简化的高级组合计算
        total_epsilon = np.sqrt(2 * len(epsilons) * np.log(1/min(deltas))) * max(epsilons)
        total_delta = sum(deltas)
    else:
        raise ValueError(f"不支持的组合类型: {composition_type}")
    
    return total_epsilon, total_delta
