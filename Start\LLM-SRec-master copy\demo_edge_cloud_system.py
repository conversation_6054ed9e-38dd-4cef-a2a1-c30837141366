"""
端云协同推荐系统演示脚本

本脚本演示端云协同推荐系统的主要功能：
1. 系统初始化和配置
2. 不同模式的推荐服务
3. 隐私保护机制演示
4. 知识蒸馏过程展示
5. 性能监控和统计

使用方法：
python demo_edge_cloud_system.py
"""

import asyncio
import torch
import numpy as np
import json
import time
from typing import List, Dict, Any
import logging

from client_cloud import (
    EdgeCloudRecommendationSystem,
    SystemConfig,
    RecommendationMode,
    create_edge_cloud_system
)


# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def generate_sample_data() -> List[tuple]:
    """生成示例用户数据"""
    sample_users = []
    
    # 生成10个示例用户
    for i in range(10):
        user_id = f"user_{i:03d}"
        
        # 生成随机用户序列（模拟电影/电视剧观看历史）
        sequence_length = np.random.randint(5, 20)
        user_sequence = np.random.randint(1, 1000, sequence_length).tolist()
        
        sample_users.append((user_sequence, user_id))
    
    return sample_users


async def demo_system_initialization():
    """演示系统初始化"""
    logger.info("=" * 60)
    logger.info("1. 系统初始化演示")
    logger.info("=" * 60)
    
    # 创建系统配置
    config = SystemConfig(
        device="cpu",  # 使用CPU以确保兼容性
        rec_pre_trained_data="Movies_and_TV",
        default_recommendation_mode=RecommendationMode.COLLABORATIVE,
        privacy_level="high",
        enable_knowledge_distillation=True,
        distillation_interval=5  # 降低蒸馏间隔用于演示
    )
    
    logger.info(f"系统配置: {config}")
    
    # 创建系统
    system = EdgeCloudRecommendationSystem(config)
    
    # 初始化（注意：实际部署时需要真实的云端服务）
    try:
        await system.initialize()
        logger.info("✅ 系统初始化成功")
        return system
    except Exception as e:
        logger.warning(f"⚠️ 系统初始化失败（预期行为，因为没有真实云端服务）: {e}")
        # 返回未完全初始化的系统用于演示其他功能
        return system


async def demo_edge_recommendations(system: EdgeCloudRecommendationSystem):
    """演示端侧推荐"""
    logger.info("=" * 60)
    logger.info("2. 端侧推荐演示")
    logger.info("=" * 60)
    
    # 生成示例用户数据
    sample_users = generate_sample_data()
    
    for i, (user_sequence, user_id) in enumerate(sample_users[:3]):  # 只演示前3个用户
        logger.info(f"用户 {user_id} 的观看历史: {user_sequence[:10]}...")  # 只显示前10个
        
        try:
            # 端侧推荐
            result = await system.recommend(
                user_sequence=user_sequence,
                user_id=user_id,
                num_recommendations=5,
                mode=RecommendationMode.EDGE_ONLY
            )
            
            if result['success']:
                logger.info(f"✅ 端侧推荐成功:")
                for j, rec in enumerate(result['recommendations'][:3]):  # 只显示前3个推荐
                    logger.info(f"   {j+1}. {rec.get('title', f'Item_{rec.get(\"item_id\", \"unknown\")}')}")
                logger.info(f"   响应时间: {result['response_time']:.3f}s")
            else:
                logger.warning(f"⚠️ 端侧推荐失败: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            logger.error(f"❌ 端侧推荐异常: {e}")
        
        await asyncio.sleep(0.5)  # 短暂延迟


async def demo_privacy_protection(system: EdgeCloudRecommendationSystem):
    """演示隐私保护机制"""
    logger.info("=" * 60)
    logger.info("3. 隐私保护机制演示")
    logger.info("=" * 60)
    
    if not system.edge_model:
        logger.warning("⚠️ 端侧模型未初始化，跳过隐私保护演示")
        return
    
    # 生成示例用户数据
    user_sequence = [1, 15, 23, 45, 67, 89, 123, 156, 189, 234]
    user_id = "privacy_demo_user"
    
    logger.info(f"原始用户序列: {user_sequence}")
    
    try:
        # 生成用户表示
        user_repr = system.edge_model.generate_user_representation(user_sequence, user_id)
        logger.info(f"✅ 生成64维用户表示，维度: {user_repr.shape}")
        logger.info(f"   表示向量范数: {torch.norm(user_repr, p=2).item():.4f}")
        logger.info(f"   表示向量均值: {torch.mean(user_repr).item():.4f}")
        
        # 生成安全的用户表示（应用隐私保护）
        encrypted_package, risk_assessment = system.edge_model.generate_secure_user_representation(
            user_sequence, user_id
        )
        
        logger.info(f"✅ 隐私保护处理完成:")
        logger.info(f"   风险级别: {risk_assessment['risk_level']}")
        logger.info(f"   风险分数: {risk_assessment['risk_score']:.4f}")
        logger.info(f"   风险因素: {risk_assessment['risk_factors']}")
        logger.info(f"   数据已加密: {encrypted_package['encrypted']}")
        
        # 获取隐私统计
        privacy_stats = system.edge_model.get_privacy_statistics()
        logger.info(f"✅ 隐私保护统计:")
        logger.info(f"   总请求数: {privacy_stats['total_requests']}")
        logger.info(f"   平均风险分数: {privacy_stats['average_risk_score']:.4f}")
        
    except Exception as e:
        logger.error(f"❌ 隐私保护演示失败: {e}")


async def demo_batch_recommendations(system: EdgeCloudRecommendationSystem):
    """演示批量推荐"""
    logger.info("=" * 60)
    logger.info("4. 批量推荐演示")
    logger.info("=" * 60)
    
    # 生成批量用户数据
    batch_users = generate_sample_data()[:5]  # 5个用户的批量请求
    
    logger.info(f"批量推荐 {len(batch_users)} 个用户...")
    
    start_time = time.time()
    
    try:
        # 批量推荐
        results = await system.batch_recommend(
            batch_data=batch_users,
            num_recommendations=3,
            mode=RecommendationMode.EDGE_ONLY
        )
        
        total_time = time.time() - start_time
        
        # 统计结果
        successful_count = sum(1 for r in results if r.get('success', False))
        failed_count = len(results) - successful_count
        
        logger.info(f"✅ 批量推荐完成:")
        logger.info(f"   总用户数: {len(batch_users)}")
        logger.info(f"   成功数: {successful_count}")
        logger.info(f"   失败数: {failed_count}")
        logger.info(f"   总耗时: {total_time:.3f}s")
        logger.info(f"   平均耗时: {total_time/len(batch_users):.3f}s/用户")
        
        # 显示部分结果
        for i, result in enumerate(results[:2]):  # 只显示前2个结果
            if result.get('success', False):
                user_id = result['user_id']
                rec_count = result['num_recommendations']
                response_time = result.get('response_time', 0)
                logger.info(f"   用户 {user_id}: {rec_count} 个推荐, {response_time:.3f}s")
        
    except Exception as e:
        logger.error(f"❌ 批量推荐失败: {e}")


async def demo_system_statistics(system: EdgeCloudRecommendationSystem):
    """演示系统统计信息"""
    logger.info("=" * 60)
    logger.info("5. 系统统计信息演示")
    logger.info("=" * 60)
    
    try:
        # 获取系统统计
        stats = system.get_system_statistics()
        
        logger.info("✅ 系统性能统计:")
        logger.info(f"   总推荐次数: {stats['total_recommendations']}")
        logger.info(f"   端侧推荐次数: {stats['edge_recommendations']}")
        logger.info(f"   云端推荐次数: {stats['cloud_recommendations']}")
        logger.info(f"   协同推荐次数: {stats['collaborative_recommendations']}")
        logger.info(f"   平均响应时间: {stats['average_response_time']:.3f}s")
        logger.info(f"   系统运行时间: {stats['system_uptime']:.1f}s")
        
        # 端侧模型统计
        if 'edge_privacy_stats' in stats:
            edge_stats = stats['edge_privacy_stats']
            logger.info("✅ 端侧隐私保护统计:")
            logger.info(f"   隐私请求数: {edge_stats['total_requests']}")
            logger.info(f"   平均风险分数: {edge_stats['average_risk_score']:.4f}")
        
        # 云端模型统计
        if 'cloud_inference_stats' in stats:
            cloud_stats = stats['cloud_inference_stats']
            logger.info("✅ 云端推理统计:")
            logger.info(f"   总请求数: {cloud_stats['total_requests']}")
            logger.info(f"   缓存命中率: {cloud_stats['cache_hit_rate']:.2%}")
        
        # 通信统计
        if 'communication_stats' in stats:
            comm_stats = stats['communication_stats']
            logger.info("✅ 端云通信统计:")
            logger.info(f"   成功率: {comm_stats.get('success_rate', 0):.2%}")
            logger.info(f"   连接状态: {comm_stats.get('connection_status', 'unknown')}")
        
        # 知识蒸馏统计
        if 'distillation_stats' in stats:
            dist_stats = stats['distillation_stats']
            logger.info("✅ 知识蒸馏统计:")
            logger.info(f"   蒸馏轮数: {dist_stats['total_epochs']}")
            logger.info(f"   最佳准确率: {dist_stats['best_accuracy']:.4f}")
        
    except Exception as e:
        logger.error(f"❌ 获取系统统计失败: {e}")


async def demo_system_persistence(system: EdgeCloudRecommendationSystem):
    """演示系统状态持久化"""
    logger.info("=" * 60)
    logger.info("6. 系统状态持久化演示")
    logger.info("=" * 60)
    
    try:
        # 保存系统状态
        save_dir = "./demo_system_state"
        system.save_system_state(save_dir)
        logger.info(f"✅ 系统状态已保存到 {save_dir}")
        
        # 检查保存的文件
        from pathlib import Path
        save_path = Path(save_dir)
        if save_path.exists():
            files = list(save_path.glob("*"))
            logger.info(f"   保存的文件: {[f.name for f in files]}")
        
    except Exception as e:
        logger.error(f"❌ 系统状态保存失败: {e}")


async def main():
    """主演示函数"""
    logger.info("🚀 端云协同推荐系统演示开始")
    logger.info("=" * 80)
    
    system = None
    
    try:
        # 1. 系统初始化演示
        system = await demo_system_initialization()
        
        # 2. 端侧推荐演示
        await demo_edge_recommendations(system)
        
        # 3. 隐私保护机制演示
        await demo_privacy_protection(system)
        
        # 4. 批量推荐演示
        await demo_batch_recommendations(system)
        
        # 5. 系统统计信息演示
        await demo_system_statistics(system)
        
        # 6. 系统状态持久化演示
        await demo_system_persistence(system)
        
    except Exception as e:
        logger.error(f"❌ 演示过程中发生错误: {e}")
    
    finally:
        # 清理资源
        if system:
            await system.close()
        
        logger.info("=" * 80)
        logger.info("🎉 端云协同推荐系统演示完成")


if __name__ == "__main__":
    # 运行演示
    asyncio.run(main())
