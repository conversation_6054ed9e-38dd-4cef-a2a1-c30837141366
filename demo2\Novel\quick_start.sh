#!/bin/bash

# Novel LLM-SRec 一键快速开始脚本 (Linux服务器版)
# 用法: bash quick_start.sh

set -e  # 遇到错误时退出

echo "=========================================="
echo "Novel端云协同推荐系统快速开始 (Linux服务器)"
echo "=========================================="

# 检查Python环境
echo "检查Python环境..."
python3 --version 2>/dev/null || python --version || {
    echo "错误: 未找到Python，请先安装Python 3.8+"
    exit 1
}

# 使用python3或python
PYTHON_CMD="python3"
command -v python3 >/dev/null 2>&1 || PYTHON_CMD="python"

# 检查GPU环境
echo "检查GPU环境..."
if command -v nvidia-smi >/dev/null 2>&1; then
    echo "GPU状态:"
    nvidia-smi --query-gpu=name,memory.total --format=csv,noheader,nounits

    # 检查多GPU配置
    echo "验证多GPU配置..."
    $PYTHON_CMD -c "
from utils import GPUManager
gpu_manager = GPUManager()
gpu_available = gpu_manager.check_gpu_environment()
multi_gpu_ok = gpu_manager.test_multi_gpu()
print(f'GPU可用: {gpu_available}')
print(f'多GPU测试: {multi_gpu_ok}')

if gpu_available:
    config = gpu_manager.recommend_multi_gpu_config()
    print('推荐配置:')
    if 'cf_srec_training' in config:
        cf_config = config['cf_srec_training']
        print(f'  CF-SRec训练: batch_size={cf_config[\"batch_size\"]}, hidden_units={cf_config[\"hidden_units\"]}')
"
else
    echo "⚠️  未检测到NVIDIA GPU，将使用CPU模式"
fi

# 检查依赖
echo "检查依赖..."
$PYTHON_CMD -c "import torch; print(f'PyTorch: {torch.__version__}')" 2>/dev/null || {
    echo "正在安装依赖..."
    pip3 install -r requirements.txt 2>/dev/null || pip install -r requirements.txt
}

echo ""
echo "=========================================="
echo "步骤1: 运行快速测试（约3-5分钟）"
echo "=========================================="

# 运行快速测试
$PYTHON_CMD run_experiment.py --config config/experiments/quick_test.yaml

echo ""
echo "=========================================="
echo "步骤2: 查看测试结果"
echo "=========================================="

# 检查结果文件是否存在
if [ -f "experiments/runs/quick_test/results/latest_results.json" ]; then
    echo "✅ 快速测试完成！"
    echo ""
    echo "实验结果:"
    $PYTHON_CMD -c "
import json
try:
    with open('experiments/runs/quick_test/results/latest_results.json', 'r') as f:
        results = json.load(f)
    eval_results = results.get('evaluation_results', {})
    print('=== 评估指标 ===')
    for metric, value in eval_results.items():
        if isinstance(value, (int, float)):
            print(f'{metric}: {value:.4f}')
    print(f'训练时间: {results.get(\"experiment_time\", 0):.2f}秒')
except Exception as e:
    print(f'读取结果失败: {e}')
"
else
    echo "❌ 快速测试失败，请检查日志文件"
    if [ -f "experiments/runs/quick_test/experiment.log" ]; then
        echo "错误日志:"
        tail -10 experiments/runs/quick_test/experiment.log
    fi
    exit 1
fi

echo ""
echo "=========================================="
echo "步骤3: 准备完整实验数据集"
echo "=========================================="

echo "准备Beauty数据集（中等规模，适合完整实验）..."
$PYTHON_CMD prepare_data.py --dataset Beauty

echo ""
echo "=========================================="
echo "步骤4: 运行CF-SRec训练（可选）"
echo "=========================================="

# 检测GPU数量并推荐配置
GPU_COUNT=$($PYTHON_CMD -c "
import torch
if torch.cuda.is_available():
    print(torch.cuda.device_count())
else:
    print(0)
" 2>/dev/null || echo "0")

echo "检测到 $GPU_COUNT 个GPU"

if [ "$GPU_COUNT" -ge 2 ]; then
    echo ""
    echo "🚀 多GPU训练选项:"
    echo ""
    echo "# 双GPU CF-SRec预训练（推荐）"
    echo "$PYTHON_CMD run_experiment.py \\"
    echo "    --stage pretrain_cf_srec \\"
    echo "    --dataset Movies_and_TV \\"
    echo "    --device cuda \\"
    echo "    --multi_gpu \\"
    echo "    --batch_size 512 \\"
    echo "    --num_epochs 200 \\"
    echo "    --hidden_units 128 \\"
    echo "    --num_blocks 4 \\"
    echo "    --maxlen 128 \\"
    echo "    --fp16"
    echo ""
    echo "# 分布式训练（更高效）"
    echo "CUDA_VISIBLE_DEVICES=0,1 torchrun \\"
    echo "    --nproc_per_node=2 \\"
    echo "    run_experiment.py \\"
    echo "    --stage pretrain_cf_srec \\"
    echo "    --dataset Movies_and_TV \\"
    echo "    --distributed \\"
    echo "    --batch_size 256 \\"
    echo "    --gradient_accumulation_steps 2 \\"
    echo "    --num_epochs 200 \\"
    echo "    --hidden_units 128 \\"
    echo "    --num_blocks 4 \\"
    echo "    --fp16"
    echo ""
    echo "运行多GPU训练? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo "开始多GPU CF-SRec训练..."
        $PYTHON_CMD run_experiment.py \
            --stage pretrain_cf_srec \
            --dataset Movies_and_TV \
            --device cuda \
            --multi_gpu \
            --batch_size 256 \
            --num_epochs 5 \
            --hidden_units 64 \
            --num_blocks 2 \
            --maxlen 50 \
            --experiment_name "cf_srec_multi_gpu_demo"
    fi
elif [ "$GPU_COUNT" -eq 1 ]; then
    echo ""
    echo "🔧 单GPU训练选项:"
    echo ""
    echo "# 单GPU CF-SRec预训练"
    echo "$PYTHON_CMD run_experiment.py \\"
    echo "    --stage pretrain_cf_srec \\"
    echo "    --dataset Movies_and_TV \\"
    echo "    --device cuda:0 \\"
    echo "    --batch_size 128 \\"
    echo "    --num_epochs 200 \\"
    echo "    --hidden_units 64 \\"
    echo "    --num_blocks 2 \\"
    echo "    --maxlen 50"
    echo ""
    echo "运行单GPU训练? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo "开始单GPU CF-SRec训练..."
        $PYTHON_CMD run_experiment.py \
            --stage pretrain_cf_srec \
            --dataset Movies_and_TV \
            --device cuda:0 \
            --batch_size 64 \
            --num_epochs 3 \
            --hidden_units 32 \
            --num_blocks 1 \
            --maxlen 20 \
            --experiment_name "cf_srec_single_gpu_demo"
    fi
else
    echo ""
    echo "💻 CPU训练选项:"
    echo ""
    echo "# CPU CF-SRec预训练（较慢）"
    echo "$PYTHON_CMD run_experiment.py \\"
    echo "    --stage pretrain_cf_srec \\"
    echo "    --dataset Industrial_and_Scientific \\"
    echo "    --device cpu \\"
    echo "    --batch_size 32 \\"
    echo "    --num_epochs 50 \\"
    echo "    --hidden_units 32 \\"
    echo "    --num_blocks 1 \\"
    echo "    --maxlen 20"
fi

echo ""
echo "=========================================="
echo "步骤5: 其他实验选项"
echo "=========================================="

echo "您还可以运行以下实验:"
echo ""
echo "# 运行Beauty数据集实验"
echo "$PYTHON_CMD run_experiment.py --dataset Beauty --experiment-name beauty_experiment --num-epochs 10"
echo ""
echo "# 查看实验结果"
echo "$PYTHON_CMD -c \"
import json
with open('experiments/runs/beauty_experiment/results/latest_results.json', 'r') as f:
    results = json.load(f)
eval_results = results.get('evaluation_results', {})
print('=== 实验结果 ===')
for metric, value in eval_results.items():
    if isinstance(value, (int, float)):
        print(f'{metric}: {value:.4f}')
print(f'训练时间: {results.get(\\\"experiment_time\\\", 0):.2f}秒')
\""

echo ""
echo "=========================================="
echo "🎉 快速开始完成！"
echo "=========================================="

echo "接下来您可以："
echo "1. 查看详细文档: README.md"
echo "2. 运行完整实验: $PYTHON_CMD run_experiment.py --dataset Beauty"
echo "3. 查看可用数据集: $PYTHON_CMD prepare_data.py --list"
echo "4. 自定义实验配置: experiments/configs/"
echo "5. 查看GPU使用情况: nvidia-smi"
echo ""
echo "Linux服务器使用提示："
echo "- 使用 nohup 在后台运行长时间实验"
echo "- 使用 screen 或 tmux 管理会话"
echo "- 监控GPU内存使用避免OOM"
echo ""
echo "如需帮助，请查看 README.md 或提交Issue"
