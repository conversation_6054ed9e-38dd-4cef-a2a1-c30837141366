"""
安全通信子模块

本模块提供端云之间的安全通信机制：
1. 加密管理：对称和非对称加密算法
2. 消息认证：HMAC和数字签名验证
3. 密钥管理：密钥生成、分发和轮换
4. 安全协议：TLS/SSL和自定义安全协议

核心功能：
- 多层加密：支持多种加密算法和模式
- 完整性保护：防止数据篡改和重放攻击
- 身份认证：确保通信双方身份可信
- 前向安全：密钥泄露不影响历史通信安全
"""

import os
import time
import hmac
import hashlib
import base64
import json
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
import logging


@dataclass
class SecurityConfig:
    """安全配置"""
    # 加密配置
    encryption_algorithm: str = 'AES'  # 'AES', 'ChaCha20', 'RSA'
    key_size: int = 256  # 密钥长度（位）
    
    # 认证配置
    authentication_method: str = 'HMAC'  # 'HMAC', 'RSA_SIGNATURE'
    hash_algorithm: str = 'SHA256'  # 'SHA256', 'SHA512'
    
    # 密钥管理配置
    key_rotation_interval: int = 3600  # 密钥轮换间隔（秒）
    key_derivation_iterations: int = 100000  # 密钥派生迭代次数
    
    # 安全策略
    enable_forward_secrecy: bool = True  # 启用前向安全
    enable_replay_protection: bool = True  # 启用重放攻击保护
    message_timeout: int = 300  # 消息超时时间（秒）


class EncryptionManager:
    """
    加密管理器
    
    提供多种加密算法的统一接口
    """
    
    def __init__(self, config: SecurityConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 初始化加密组件
        self.symmetric_key = None
        self.asymmetric_keys = None
        self.cipher_suite = None
        
        self._initialize_encryption()
    
    def _initialize_encryption(self):
        """初始化加密组件"""
        if self.config.encryption_algorithm == 'AES':
            self._initialize_aes()
        elif self.config.encryption_algorithm == 'ChaCha20':
            self._initialize_chacha20()
        elif self.config.encryption_algorithm == 'RSA':
            self._initialize_rsa()
        else:
            raise ValueError(f"不支持的加密算法: {self.config.encryption_algorithm}")
    
    def _initialize_aes(self):
        """初始化AES加密"""
        # 生成对称密钥
        password = b"edge_cloud_secure_communication"
        salt = os.urandom(16)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,  # AES-256
            salt=salt,
            iterations=self.config.key_derivation_iterations,
        )
        
        key = kdf.derive(password)
        self.symmetric_key = key
        self.cipher_suite = Fernet(base64.urlsafe_b64encode(key))
        
        self.logger.info("AES加密初始化完成")
    
    def _initialize_chacha20(self):
        """初始化ChaCha20加密"""
        # ChaCha20密钥长度为32字节
        self.symmetric_key = os.urandom(32)
        self.logger.info("ChaCha20加密初始化完成")
    
    def _initialize_rsa(self):
        """初始化RSA加密"""
        # 生成RSA密钥对
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=self.config.key_size * 8,  # 转换为位
        )
        public_key = private_key.public_key()
        
        self.asymmetric_keys = {
            'private': private_key,
            'public': public_key
        }
        
        self.logger.info("RSA加密初始化完成")
    
    def encrypt(self, data: bytes) -> Dict[str, Any]:
        """
        加密数据
        
        Args:
            data: 待加密的数据
            
        Returns:
            加密结果字典
        """
        if self.config.encryption_algorithm == 'AES':
            return self._encrypt_aes(data)
        elif self.config.encryption_algorithm == 'ChaCha20':
            return self._encrypt_chacha20(data)
        elif self.config.encryption_algorithm == 'RSA':
            return self._encrypt_rsa(data)
        else:
            raise ValueError(f"不支持的加密算法: {self.config.encryption_algorithm}")
    
    def decrypt(self, encrypted_data: Dict[str, Any]) -> bytes:
        """
        解密数据
        
        Args:
            encrypted_data: 加密数据字典
            
        Returns:
            解密后的数据
        """
        if self.config.encryption_algorithm == 'AES':
            return self._decrypt_aes(encrypted_data)
        elif self.config.encryption_algorithm == 'ChaCha20':
            return self._decrypt_chacha20(encrypted_data)
        elif self.config.encryption_algorithm == 'RSA':
            return self._decrypt_rsa(encrypted_data)
        else:
            raise ValueError(f"不支持的加密算法: {self.config.encryption_algorithm}")
    
    def _encrypt_aes(self, data: bytes) -> Dict[str, Any]:
        """AES加密"""
        encrypted_data = self.cipher_suite.encrypt(data)
        return {
            'algorithm': 'AES',
            'encrypted_data': base64.b64encode(encrypted_data).decode('utf-8'),
            'timestamp': time.time()
        }
    
    def _decrypt_aes(self, encrypted_data: Dict[str, Any]) -> bytes:
        """AES解密"""
        encrypted_bytes = base64.b64decode(encrypted_data['encrypted_data'])
        return self.cipher_suite.decrypt(encrypted_bytes)
    
    def _encrypt_chacha20(self, data: bytes) -> Dict[str, Any]:
        """ChaCha20加密"""
        nonce = os.urandom(12)  # ChaCha20 nonce长度为12字节
        cipher = Cipher(algorithms.ChaCha20(self.symmetric_key, nonce), mode=None)
        encryptor = cipher.encryptor()
        encrypted_data = encryptor.update(data) + encryptor.finalize()
        
        return {
            'algorithm': 'ChaCha20',
            'encrypted_data': base64.b64encode(encrypted_data).decode('utf-8'),
            'nonce': base64.b64encode(nonce).decode('utf-8'),
            'timestamp': time.time()
        }
    
    def _decrypt_chacha20(self, encrypted_data: Dict[str, Any]) -> bytes:
        """ChaCha20解密"""
        encrypted_bytes = base64.b64decode(encrypted_data['encrypted_data'])
        nonce = base64.b64decode(encrypted_data['nonce'])
        
        cipher = Cipher(algorithms.ChaCha20(self.symmetric_key, nonce), mode=None)
        decryptor = cipher.decryptor()
        return decryptor.update(encrypted_bytes) + decryptor.finalize()
    
    def _encrypt_rsa(self, data: bytes) -> Dict[str, Any]:
        """RSA加密"""
        # RSA加密有长度限制，对于长数据需要分块处理
        max_chunk_size = (self.config.key_size * 8 // 8) - 42  # OAEP填充开销
        
        encrypted_chunks = []
        for i in range(0, len(data), max_chunk_size):
            chunk = data[i:i + max_chunk_size]
            encrypted_chunk = self.asymmetric_keys['public'].encrypt(
                chunk,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            encrypted_chunks.append(base64.b64encode(encrypted_chunk).decode('utf-8'))
        
        return {
            'algorithm': 'RSA',
            'encrypted_chunks': encrypted_chunks,
            'timestamp': time.time()
        }
    
    def _decrypt_rsa(self, encrypted_data: Dict[str, Any]) -> bytes:
        """RSA解密"""
        decrypted_chunks = []
        for encrypted_chunk in encrypted_data['encrypted_chunks']:
            encrypted_bytes = base64.b64decode(encrypted_chunk)
            decrypted_chunk = self.asymmetric_keys['private'].decrypt(
                encrypted_bytes,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            decrypted_chunks.append(decrypted_chunk)
        
        return b''.join(decrypted_chunks)


class MessageAuthenticator:
    """
    消息认证器
    
    提供消息完整性和身份认证
    """
    
    def __init__(self, config: SecurityConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 初始化认证密钥
        self.auth_key = os.urandom(32)
        self.message_counter = 0
        self.received_messages = set()  # 用于重放攻击保护
    
    def create_authentication_tag(
        self, 
        message: bytes, 
        additional_data: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        创建消息认证标签
        
        Args:
            message: 待认证的消息
            additional_data: 附加数据
            
        Returns:
            认证标签
        """
        if self.config.authentication_method == 'HMAC':
            return self._create_hmac_tag(message, additional_data)
        elif self.config.authentication_method == 'RSA_SIGNATURE':
            return self._create_rsa_signature(message, additional_data)
        else:
            raise ValueError(f"不支持的认证方法: {self.config.authentication_method}")
    
    def verify_authentication_tag(
        self, 
        message: bytes, 
        auth_tag: str,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        验证消息认证标签
        
        Args:
            message: 消息内容
            auth_tag: 认证标签
            additional_data: 附加数据
            
        Returns:
            验证是否成功
        """
        try:
            if self.config.authentication_method == 'HMAC':
                return self._verify_hmac_tag(message, auth_tag, additional_data)
            elif self.config.authentication_method == 'RSA_SIGNATURE':
                return self._verify_rsa_signature(message, auth_tag, additional_data)
            else:
                return False
        except Exception as e:
            self.logger.error(f"认证验证失败: {e}")
            return False
    
    def _create_hmac_tag(self, message: bytes, additional_data: Optional[Dict[str, Any]]) -> str:
        """创建HMAC标签"""
        # 构建完整的认证数据
        auth_data = message
        if additional_data:
            auth_data += json.dumps(additional_data, sort_keys=True).encode('utf-8')
        
        # 添加消息计数器（防重放）
        if self.config.enable_replay_protection:
            self.message_counter += 1
            auth_data += str(self.message_counter).encode('utf-8')
        
        # 计算HMAC
        if self.config.hash_algorithm == 'SHA256':
            hash_func = hashlib.sha256
        elif self.config.hash_algorithm == 'SHA512':
            hash_func = hashlib.sha512
        else:
            hash_func = hashlib.sha256
        
        hmac_tag = hmac.new(self.auth_key, auth_data, hash_func).hexdigest()
        
        return hmac_tag
    
    def _verify_hmac_tag(
        self, 
        message: bytes, 
        auth_tag: str, 
        additional_data: Optional[Dict[str, Any]]
    ) -> bool:
        """验证HMAC标签"""
        # 重放攻击保护
        if self.config.enable_replay_protection:
            message_id = hashlib.sha256(message).hexdigest()
            if message_id in self.received_messages:
                self.logger.warning("检测到重放攻击")
                return False
            self.received_messages.add(message_id)
        
        # 构建认证数据（需要尝试不同的计数器值）
        auth_data = message
        if additional_data:
            auth_data += json.dumps(additional_data, sort_keys=True).encode('utf-8')
        
        # 验证HMAC（简化处理，实际应用中需要更复杂的同步机制）
        if self.config.hash_algorithm == 'SHA256':
            hash_func = hashlib.sha256
        elif self.config.hash_algorithm == 'SHA512':
            hash_func = hashlib.sha512
        else:
            hash_func = hashlib.sha256
        
        # 尝试最近几个计数器值
        for counter in range(max(0, self.message_counter - 10), self.message_counter + 10):
            test_data = auth_data + str(counter).encode('utf-8')
            expected_tag = hmac.new(self.auth_key, test_data, hash_func).hexdigest()
            if hmac.compare_digest(expected_tag, auth_tag):
                return True
        
        return False
    
    def _create_rsa_signature(self, message: bytes, additional_data: Optional[Dict[str, Any]]) -> str:
        """创建RSA数字签名"""
        # 注意：这里需要RSA私钥，实际实现中需要从EncryptionManager获取
        # 这是一个简化的示例
        signature_data = message
        if additional_data:
            signature_data += json.dumps(additional_data, sort_keys=True).encode('utf-8')
        
        # 计算消息摘要
        digest = hashes.Hash(hashes.SHA256())
        digest.update(signature_data)
        message_hash = digest.finalize()
        
        # 这里返回摘要的十六进制表示作为简化的"签名"
        return message_hash.hex()
    
    def _verify_rsa_signature(
        self, 
        message: bytes, 
        signature: str, 
        additional_data: Optional[Dict[str, Any]]
    ) -> bool:
        """验证RSA数字签名"""
        # 简化的验证逻辑
        signature_data = message
        if additional_data:
            signature_data += json.dumps(additional_data, sort_keys=True).encode('utf-8')
        
        digest = hashes.Hash(hashes.SHA256())
        digest.update(signature_data)
        expected_hash = digest.finalize().hex()
        
        return hmac.compare_digest(expected_hash, signature)


class KeyRotationManager:
    """
    密钥轮换管理器
    
    管理密钥的生命周期和轮换
    """
    
    def __init__(self, config: SecurityConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        self.current_key_id = 0
        self.key_history = {}
        self.last_rotation_time = time.time()
    
    def should_rotate_key(self) -> bool:
        """检查是否需要轮换密钥"""
        current_time = time.time()
        return (current_time - self.last_rotation_time) > self.config.key_rotation_interval
    
    def rotate_key(self, encryption_manager: EncryptionManager) -> str:
        """
        轮换密钥
        
        Args:
            encryption_manager: 加密管理器
            
        Returns:
            新密钥ID
        """
        # 保存当前密钥
        old_key_id = self.current_key_id
        self.key_history[old_key_id] = {
            'key': encryption_manager.symmetric_key,
            'created_time': self.last_rotation_time,
            'retired_time': time.time()
        }
        
        # 生成新密钥
        self.current_key_id += 1
        encryption_manager._initialize_encryption()
        self.last_rotation_time = time.time()
        
        self.logger.info(f"密钥轮换完成: {old_key_id} -> {self.current_key_id}")
        
        return str(self.current_key_id)
    
    def get_key_by_id(self, key_id: str) -> Optional[bytes]:
        """根据ID获取历史密钥"""
        key_id_int = int(key_id)
        if key_id_int in self.key_history:
            return self.key_history[key_id_int]['key']
        return None
    
    def cleanup_old_keys(self, retention_period: int = 86400):
        """清理过期的历史密钥"""
        current_time = time.time()
        expired_keys = []
        
        for key_id, key_info in self.key_history.items():
            if (current_time - key_info['retired_time']) > retention_period:
                expired_keys.append(key_id)
        
        for key_id in expired_keys:
            del self.key_history[key_id]
            self.logger.info(f"清理过期密钥: {key_id}")
    
    def get_key_statistics(self) -> Dict[str, Any]:
        """获取密钥统计信息"""
        return {
            'current_key_id': self.current_key_id,
            'historical_key_count': len(self.key_history),
            'last_rotation_time': self.last_rotation_time,
            'next_rotation_time': self.last_rotation_time + self.config.key_rotation_interval,
            'rotation_interval': self.config.key_rotation_interval
        }
