#!/usr/bin/env python3
"""
LLM-SRec实验运行脚本

支持通过命令行运行各种实验，包括数据准备、模型训练、评估等。

使用示例:
    # 运行完整实验
    python run_experiment.py --config config/collaborative_config.yaml --experiment-name my_experiment

    # 仅准备数据集
    python run_experiment.py --dataset Movies_and_TV --prepare-data-only

    # 仅训练模型
    python run_experiment.py --config experiments/configs/my_experiment.yaml --train-only

    # 仅评估模型
    python run_experiment.py --config experiments/configs/my_experiment.yaml --eval-only --model-path checkpoints/model.pth

    # 列出可用数据集
    python run_experiment.py --list-datasets

    # 获取数据集信息
    python run_experiment.py --dataset-info Movies_and_TV
"""

import argparse
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from experiments import ExperimentRunner, ConfigManager
except ImportError:
    ExperimentRunner = None
    ConfigManager = None

from datasets import DatasetManager
from models.cf_srec import CFSRec
import torch
import torch.nn as nn
import torch.distributed as dist
import numpy as np

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
    ]
)

logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="Novel端云协同推荐系统实验运行脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )

    # 基本参数
    parser.add_argument(
        '--config', '-c',
        type=str,
        help='配置文件路径'
    )

    parser.add_argument(
        '--experiment-name', '-n',
        type=str,
        help='实验名称'
    )

    parser.add_argument(
        '--dataset', '-d',
        type=str,
        help='数据集名称'
    )

    # 新增：训练阶段参数（兼容QUICKSTART.md）
    parser.add_argument(
        '--stage',
        type=str,
        choices=['pretrain_cf_srec', 'train_llm', 'collaborative_training'],
        help='训练阶段'
    )

    # 新增：多GPU训练参数
    parser.add_argument(
        '--multi_gpu',
        action='store_true',
        help='使用多GPU训练'
    )

    parser.add_argument(
        '--distributed',
        action='store_true',
        help='使用分布式训练'
    )

    # 新增：模型参数（兼容QUICKSTART.md）
    parser.add_argument(
        '--hidden_units',
        type=int,
        default=64,
        help='隐藏层大小'
    )

    parser.add_argument(
        '--num_blocks',
        type=int,
        default=2,
        help='Transformer块数量'
    )

    parser.add_argument(
        '--maxlen',
        type=int,
        default=50,
        help='最大序列长度'
    )

    parser.add_argument(
        '--fp16',
        action='store_true',
        help='使用混合精度训练'
    )

    parser.add_argument(
        '--gradient_accumulation_steps',
        type=int,
        default=1,
        help='梯度累积步数'
    )

    # 新增：训练参数（兼容QUICKSTART.md）
    parser.add_argument(
        '--num_epochs',
        type=int,
        default=200,
        help='训练轮数'
    )

    parser.add_argument(
        '--learning_rate',
        type=float,
        default=0.001,
        help='学习率'
    )

    # LLM训练相关参数
    parser.add_argument(
        '--rec_pre_trained_data',
        type=str,
        help='预训练CF-SRec数据集名称'
    )

    parser.add_argument(
        '--stage2_lr',
        type=float,
        default=0.0001,
        help='LLM训练阶段学习率'
    )

    parser.add_argument(
        '--save_dir',
        type=str,
        default='model_train',
        help='模型保存目录'
    )


    
    # 运行模式
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument(
        '--prepare-data-only',
        action='store_true',
        help='仅准备数据集'
    )
    
    mode_group.add_argument(
        '--train-only',
        action='store_true',
        help='仅训练模型（假设数据已准备）'
    )
    
    mode_group.add_argument(
        '--eval-only',
        action='store_true',
        help='仅评估模型'
    )
    
    # 评估相关参数
    parser.add_argument(
        '--model-path',
        type=str,
        help='模型文件路径（用于评估）'
    )
    
    # 信息查询
    info_group = parser.add_mutually_exclusive_group()
    info_group.add_argument(
        '--list-datasets',
        action='store_true',
        help='列出所有可用数据集'
    )
    
    info_group.add_argument(
        '--dataset-info',
        type=str,
        help='获取指定数据集的信息'
    )
    
    info_group.add_argument(
        '--list-configs',
        action='store_true',
        help='列出所有实验配置'
    )
    
    info_group.add_argument(
        '--experiment-status',
        action='store_true',
        help='获取实验状态'
    )
    
    # 配置覆盖
    parser.add_argument(
        '--batch-size',
        type=int,
        help='批次大小'
    )
    
    parser.add_argument(
        '--learning-rate',
        type=float,
        help='学习率'
    )
    
    parser.add_argument(
        '--num-epochs',
        type=int,
        help='训练轮数'
    )
    
    parser.add_argument(
        '--device',
        type=str,
        help='设备 (cuda:0, cpu等)'
    )
    
    # 其他选项
    parser.add_argument(
        '--force-download',
        action='store_true',
        help='强制重新下载数据集'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出'
    )
    
    return parser.parse_args()


def setup_logging(verbose: bool):
    """设置日志级别"""
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    else:
        logging.getLogger().setLevel(logging.INFO)


def handle_info_commands(args):
    """处理信息查询命令"""
    if args.list_datasets:
        dataset_manager = DatasetManager()
        datasets = dataset_manager.list_available_datasets()
        
        print("Available Datasets:")
        for dataset in datasets:
            info = dataset_manager.get_dataset_info(dataset)
            print(f"  - {dataset}: {info.description}")
        return True
    
    if args.dataset_info:
        dataset_manager = DatasetManager()
        info = dataset_manager.get_dataset_info(args.dataset_info)
        
        if info:
            print(f"Dataset Information: {args.dataset_info}")
            print(f"  Source: {info.source}")
            print(f"  Description: {info.description}")
            print(f"  Task Type: {info.task_type}")
            print(f"  URL/Path: {info.url_or_path}")
        else:
            print(f"Dataset not found: {args.dataset_info}")
        return True
    
    if args.list_configs:
        config_manager = ConfigManager()
        configs = config_manager.list_experiment_configs()
        
        print("Available Experiment Configs:")
        for config in configs:
            print(f"  - {config}")
        return True
    
    return False


def create_experiment_runner(args) -> ExperimentRunner:
    """创建实验运行器"""
    config_path = args.config
    experiment_name = args.experiment_name
    
    # 如果指定了数据集但没有配置文件，创建数据集特定的配置
    if args.dataset and not config_path:
        config_manager = ConfigManager()
        if not experiment_name:
            experiment_name = f"experiment_{args.dataset}"
        
        config_path = config_manager.create_dataset_experiment(args.dataset, experiment_name)
        logger.info(f"Created dataset-specific config: {config_path}")
    
    # 创建实验运行器
    if ExperimentRunner is None:
        logger.error("ExperimentRunner not available, using stage-based training")
        return

    runner = ExperimentRunner(config_path, experiment_name)
    
    # 应用命令行参数覆盖
    if args.batch_size:
        runner.config['training']['batch_size'] = args.batch_size
    
    if args.learning_rate:
        runner.config['training']['learning_rate'] = args.learning_rate
    
    if args.num_epochs:
        runner.config['training']['num_epochs'] = args.num_epochs
    
    if args.device:
        runner.config['device'] = args.device
    
    return runner


def main():
    """主函数"""
    args = parse_args()
    setup_logging(args.verbose)
    
    try:
        # 优先处理stage参数（兼容QUICKSTART.md）
        if args.stage:
            if args.stage == 'pretrain_cf_srec':
                logger.info("Running CF-SRec pretraining...")
                try:
                    success = run_cf_srec_training(args)
                    if success:
                        print("CF-SRec pretraining completed successfully!")
                    else:
                        print("CF-SRec pretraining failed!")
                        sys.exit(1)
                except Exception as e:
                    logger.error(f"CF-SRec training error: {e}")
                    import traceback
                    traceback.print_exc()
                    sys.exit(1)
                return
            elif args.stage == 'train_llm':
                logger.info("Running LLM training...")
                try:
                    success = run_llm_training(args)
                    if success:
                        print("LLM training completed successfully!")
                    else:
                        print("LLM training failed!")
                        sys.exit(1)
                except Exception as e:
                    logger.error(f"LLM training error: {e}")
                    import traceback
                    traceback.print_exc()
                    sys.exit(1)
                return
            elif args.stage == 'collaborative_training':
                logger.info("Running collaborative training...")
                try:
                    success = run_collaborative_training(args)
                    if success:
                        print("Collaborative training completed successfully!")
                    else:
                        print("Collaborative training failed!")
                        sys.exit(1)
                except Exception as e:
                    logger.error(f"Collaborative training error: {e}")
                    import traceback
                    traceback.print_exc()
                    sys.exit(1)
                return

        # 处理信息查询命令
        if handle_info_commands(args):
            return

        # 创建实验运行器
        runner = create_experiment_runner(args)
        
        # 处理实验状态查询
        if args.experiment_status:
            status = runner.get_experiment_status()
            print("Experiment Status:")
            print(f"  Name: {status['experiment_name']}")
            print(f"  Dataset: {status['dataset']}")
            print(f"  Dataset Downloaded: {status['dataset_downloaded']}")
            print(f"  Dataset Processed: {status['dataset_processed']}")
            print(f"  Experiment Dir: {status['experiment_dir']}")
            return
        


        # 执行相应的操作
        if args.prepare_data_only:
            logger.info("Running data preparation only...")
            success = runner.run_data_preparation_only()
            if success:
                print("Data preparation completed successfully!")
            else:
                print("Data preparation failed!")
                sys.exit(1)
        
        elif args.train_only:
            logger.info("Running training only...")
            results = runner.run_training_only()
            print("Training completed successfully!")
            print(f"Final training loss: {results.get('final_loss', 'N/A')}")
        
        elif args.eval_only:
            if not args.model_path:
                print("Error: --model-path is required for evaluation only mode")
                sys.exit(1)
            
            logger.info("Running evaluation only...")
            results = runner.run_evaluation_only(args.model_path)
            print("Evaluation completed successfully!")
            
            # 打印评估结果
            print("\nEvaluation Results:")
            for metric, value in results.items():
                if isinstance(value, (int, float)):
                    print(f"  {metric}: {value:.4f}")
        
        else:
            # 运行完整实验
            logger.info("Running full experiment...")
            results = runner.run_full_experiment()
            print("Experiment completed successfully!")
            
            # 打印结果摘要
            if 'evaluation_results' in results:
                print("\nFinal Evaluation Results:")
                for metric, value in results['evaluation_results'].items():
                    if isinstance(value, (int, float)):
                        print(f"  {metric}: {value:.4f}")
            
            print(f"\nExperiment time: {results.get('experiment_time', 'N/A'):.2f} seconds")
            print(f"Results saved to: {runner.experiment_dir}")
    
    except KeyboardInterrupt:
        logger.info("Experiment interrupted by user")
        sys.exit(1)
    
    except Exception as e:
        logger.error(f"Experiment failed: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def setup_distributed():
    """设置分布式训练"""
    import torch

    if 'LOCAL_RANK' in os.environ:
        local_rank = int(os.environ['LOCAL_RANK'])
    else:
        local_rank = 0

    torch.cuda.set_device(local_rank)
    dist.init_process_group(backend='nccl')

    return local_rank


def create_cf_srec_config(args, dataset_info):
    """创建CF-SRec模型配置"""
    # 获取物品数量
    if hasattr(dataset_info, 'item_num'):
        item_num = dataset_info.item_num
    elif isinstance(dataset_info, dict) and 'item_num' in dataset_info:
        item_num = dataset_info['item_num']
    else:
        item_num = 50000  # 默认值

    config = {
        'item_num': item_num,
        'hidden_size': args.hidden_units,
        'max_seq_length': args.maxlen,
        'num_attention_heads': getattr(args, 'num_heads', 1),
        'num_hidden_layers': args.num_blocks,
        'dropout_prob': getattr(args, 'dropout_rate', 0.1),
        'user_repr_dim': args.hidden_units,
    }

    return config


# 移除模拟数据生成函数 - 现在只使用真实数据


def run_cf_srec_training(args):
    """运行CF-SRec训练"""
    import torch

    # 设置分布式训练
    local_rank = 0
    if args.distributed:
        local_rank = setup_distributed()
        device = torch.device(f'cuda:{local_rank}')
    elif args.device == 'cuda' and torch.cuda.is_available():
        device = torch.device('cuda')
    elif hasattr(args, 'device') and args.device.startswith('cuda:') and torch.cuda.is_available():
        device = torch.device(args.device)
    else:
        device = torch.device('cpu')

    logger.info(f"使用设备: {device}")

    # 准备数据集 - 强制使用真实数据
    dataset_manager = DatasetManager()

    # 获取真实数据集信息
    dataset_config = dataset_manager.get_dataset_info(args.dataset)
    if dataset_config is None:
        logger.error(f"❌ 数据集 {args.dataset} 不存在于预定义配置中")
        logger.error(f"可用数据集: {dataset_manager.list_available_datasets()}")
        return False

    logger.info(f"📊 使用数据集: {args.dataset}")
    logger.info(f"📝 数据集描述: {dataset_config.description}")
    logger.info(f"🔗 数据源: {dataset_config.source}")
    logger.info(f"🌐 URL: {dataset_config.url_or_path}")

    # 验证数据集完整性
    logger.info("🔍 验证数据集完整性...")

    # 1. 检查是否已下载
    if not dataset_manager.is_dataset_downloaded(args.dataset):
        logger.info(f"📥 数据集 {args.dataset} 未下载，开始下载...")
        try:
            if not dataset_manager.download_dataset(args.dataset, force_download=True):
                logger.error(f"❌ 数据集 {args.dataset} 下载失败")
                return False
            logger.info(f"✅ 数据集 {args.dataset} 下载完成")
        except Exception as e:
            logger.error(f"❌ 数据集下载异常: {e}")
            return False
    else:
        logger.info(f"✅ 数据集 {args.dataset} 已下载")

    # 2. 检查是否已预处理
    if not dataset_manager.is_dataset_processed(args.dataset):
        logger.info(f"⚙️ 数据集 {args.dataset} 未预处理，开始预处理...")
        try:
            if not dataset_manager.preprocess_dataset(args.dataset):
                logger.error(f"❌ 数据集 {args.dataset} 预处理失败")
                return False
            logger.info(f"✅ 数据集 {args.dataset} 预处理完成")
        except Exception as e:
            logger.error(f"❌ 数据集预处理异常: {e}")
            return False
    else:
        logger.info(f"✅ 数据集 {args.dataset} 已预处理")

    # 3. 创建数据加载器
    logger.info("📊 创建数据加载器...")
    try:
        data_loader = dataset_manager.get_data_loader(
            args.dataset,
            batch_size=getattr(args, 'batch_size', 128),
            split='train'
        )
        if data_loader is None:
            logger.error(f"❌ 无法创建数据加载器")
            return False

        logger.info(f"✅ 数据加载器创建成功")

        # 4. 获取数据集统计信息
        dataset_info = {
            'item_num': data_loader.num_items,
            'user_num': data_loader.num_users,
            'name': args.dataset,
            'num_interactions': data_loader.num_interactions
        }

        logger.info("📈 真实数据集统计信息:")
        logger.info(f"  👥 用户数量: {dataset_info['user_num']:,}")
        logger.info(f"  🎬 物品数量: {dataset_info['item_num']:,}")
        logger.info(f"  💫 交互数量: {dataset_info['num_interactions']:,}")
        logger.info(f"  📦 批次大小: {data_loader.batch_size}")
        logger.info(f"  📏 序列长度: {data_loader.max_length}")

    except Exception as e:
        logger.error(f"❌ 数据加载器创建异常: {e}")
        return False

    # 创建模型
    model_config = create_cf_srec_config(args, dataset_info)
    model = CFSRec(model_config)
    model.to(device)

    # 多GPU设置
    if args.multi_gpu and torch.cuda.device_count() > 1:
        if args.distributed:
            model = nn.parallel.DistributedDataParallel(model, device_ids=[local_rank])
        else:
            model = nn.DataParallel(model)
        logger.info(f"使用 {torch.cuda.device_count()} 个GPU")

    # 创建优化器
    learning_rate = getattr(args, 'learning_rate', 0.001)
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
    criterion = nn.CrossEntropyLoss()

    # 混合精度训练
    scaler = None
    if args.fp16:
        try:
            scaler = torch.amp.GradScaler('cuda')
        except:
            scaler = torch.cuda.amp.GradScaler()
        logger.info("启用混合精度训练")

    # 使用真实数据进行训练
    logger.info("🚀 使用真实数据进行训练")

    # 验证数据加载器
    try:
        # 测试数据加载器是否正常工作
        test_batch = next(iter(data_loader.dataloader))
        logger.info(f"✅ 数据加载器测试成功，批次形状: {test_batch['input_ids'].shape}")

        # 数据加载器验证成功

    except Exception as e:
        logger.error(f"❌ 数据加载器测试失败: {e}")
        return False

    # 创建保存目录
    experiment_name = args.experiment_name or f"cf_srec_{args.dataset}"
    save_dir = Path("checkpoints") / experiment_name
    save_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"开始CF-SRec训练，保存目录: {save_dir}")
    logger.info(f"模型参数量: {sum(p.numel() for p in model.parameters()):,}")

    # 训练循环
    model.train()
    num_epochs = getattr(args, 'num_epochs', 200)
    gradient_accumulation_steps = getattr(args, 'gradient_accumulation_steps', 1)

    logger.info(f"🏃 开始训练 {num_epochs} 个epoch")

    for epoch in range(num_epochs):
        epoch_loss = 0.0
        batch_count = 0

        # 使用真实数据加载器进行训练
        for batch_idx, batch in enumerate(data_loader.dataloader):
            # 获取真实数据
            input_ids = batch['input_ids'].to(device)
            targets = batch['target_item'].to(device)
            batch_count += 1

            # 前向传播
            if args.fp16:
                try:
                    with torch.amp.autocast('cuda'):
                        outputs = model(input_ids)
                        loss = criterion(outputs['prediction_logits'], targets)
                        loss = loss / gradient_accumulation_steps
                except:
                    with torch.cuda.amp.autocast():
                        outputs = model(input_ids)
                        loss = criterion(outputs['prediction_logits'], targets)
                        loss = loss / gradient_accumulation_steps
            else:
                outputs = model(input_ids)
                loss = criterion(outputs['prediction_logits'], targets)
                loss = loss / gradient_accumulation_steps

            # 反向传播
            if args.fp16:
                scaler.scale(loss).backward()
            else:
                loss.backward()

            # 梯度累积
            if (batch_idx + 1) % gradient_accumulation_steps == 0:
                if args.fp16:
                    scaler.step(optimizer)
                    scaler.update()
                else:
                    optimizer.step()
                optimizer.zero_grad()

            epoch_loss += loss.item() * gradient_accumulation_steps

            # 打印进度
            if (batch_idx + 1) % 50 == 0:
                logger.info(f"Epoch {epoch+1}/{num_epochs}, Batch {batch_idx+1}, Loss: {loss.item():.4f}")

        # 计算平均损失
        if batch_count > 0:
            avg_loss = epoch_loss / batch_count
            logger.info(f"✅ Epoch {epoch+1}/{num_epochs} 完成，平均损失: {avg_loss:.4f}, 批次数: {batch_count}")
        else:
            logger.warning(f"⚠️  Epoch {epoch+1}/{num_epochs} 没有处理任何批次")

    # 保存最终模型
    final_model_path = save_dir / "best_model.pth"
    if hasattr(model, 'module'):
        torch.save(model.module.state_dict(), final_model_path)
    else:
        torch.save(model.state_dict(), final_model_path)

    logger.info(f"CF-SRec训练完成！模型保存在: {final_model_path}")
    return True


def run_llm_training(args):
    """运行LLM训练 - 基于LLM-SRec的真实实现"""
    logger.info("🚀 LLM训练阶段开始...")
    logger.info(f"📊 数据集: {args.dataset}")
    logger.info(f"🔧 预训练数据: {getattr(args, 'rec_pre_trained_data', 'N/A')}")
    logger.info(f"📈 学习率: {getattr(args, 'stage2_lr', 0.0001)}")
    logger.info(f"💾 保存目录: {args.save_dir}")

    # 准备数据集
    from datasets import DatasetManager
    dataset_manager = DatasetManager()

    # 验证数据集
    if not dataset_manager.is_dataset_processed(args.dataset):
        logger.error(f"❌ 数据集 {args.dataset} 未预处理，请先运行CF-SRec预训练")
        return False

    # 获取数据集信息
    data_loader = dataset_manager.get_data_loader(args.dataset, batch_size=getattr(args, 'batch_size', 32), split='train')
    if data_loader is None:
        logger.error("❌ 无法创建数据加载器")
        return False

    dataset_info = {
        'item_num': data_loader.num_items,
        'user_num': data_loader.num_users,
        'name': args.dataset
    }

    logger.info(f"📊 数据集统计: {dataset_info['user_num']} 用户, {dataset_info['item_num']} 物品")

    # 创建保存目录
    save_dir = Path(args.save_dir)
    save_dir.mkdir(parents=True, exist_ok=True)

    # 初始化LLM-SRec模型
    logger.info("🔧 初始化LLM-SRec模型...")

    try:
        from models.llmrec_model import llmrec_model

        # 创建模型
        model = llmrec_model(args, dataset_info)
        model = model.to(args.device)

        # 加载预训练的CF-SRec权重（如果存在）
        cf_srec_path = Path("checkpoints") / f"cf_srec_{args.dataset}" / "best_model.pth"
        if cf_srec_path.exists():
            logger.info(f"📥 加载预训练CF-SRec模型: {cf_srec_path}")
            cf_checkpoint = torch.load(cf_srec_path, map_location=args.device)

            # 检查检查点格式
            if 'model_state_dict' in cf_checkpoint:
                model.recsys.model.load_state_dict(cf_checkpoint['model_state_dict'])
            else:
                # 直接的state_dict格式
                model.recsys.model.load_state_dict(cf_checkpoint)

            logger.info("✅ CF-SRec权重加载成功")
        else:
            logger.warning("⚠️  未找到预训练CF-SRec模型，使用随机初始化")

        # 设置优化器
        optimizer = torch.optim.Adam(
            filter(lambda p: p.requires_grad, model.parameters()),
            lr=getattr(args, 'stage2_lr', 0.0001)
        )

        logger.info(f"✅ LLM-SRec模型初始化完成")
        logger.info(f"📊 模型参数: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    except Exception as e:
        logger.error(f"❌ 模型初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

    # 开始训练
    logger.info("🏃 开始LLM训练...")

    num_epochs = getattr(args, 'num_epochs', 10)
    model.train()

    try:
        for epoch in range(num_epochs):
            epoch_loss = 0.0
            batch_count = 0

            for batch_idx, batch in enumerate(data_loader.dataloader):
                # 准备数据
                u = batch['user_id'].numpy()
                seq = batch['input_ids'].numpy()  # 使用正确的键名
                target = batch['target_item'].numpy()

                # 创建正负样本
                pos = np.zeros_like(seq)
                pos[:, -1] = target  # 最后一个位置是目标物品

                neg = np.random.randint(1, dataset_info['item_num'], size=seq.shape)

                # 调用模型训练
                model([u, seq, pos, neg],
                     optimizer=optimizer,
                     batch_iter=[epoch+1, num_epochs, batch_idx, len(data_loader.dataloader)],
                     mode='phase2')

                batch_count += 1

                # 限制批次数量用于演示
                if batch_count >= 20:
                    break

            logger.info(f"✅ Epoch {epoch+1}/{num_epochs} 完成，处理了 {batch_count} 个批次")

            # 保存模型
            if (epoch + 1) % 5 == 0:
                model.save_model(args, epoch2=epoch+1)

        # 保存最终模型
        model.save_model(args)
        logger.info(f"💾 LLM模型已保存到: {save_dir}")

    except Exception as e:
        logger.error(f"❌ 训练过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

    logger.info("🎉 LLM训练完成！")
    return True


def run_collaborative_training(args):
    """运行端云协同训练 - 基于简化的协同模型"""
    logger.info("🚀 端云协同训练阶段开始...")
    logger.info(f"📊 数据集: {args.dataset}")
    logger.info(f"🔧 预训练数据: {getattr(args, 'rec_pre_trained_data', 'N/A')}")

    # 准备数据集
    from datasets import DatasetManager
    dataset_manager = DatasetManager()

    # 验证数据集
    if not dataset_manager.is_dataset_processed(args.dataset):
        logger.error(f"❌ 数据集 {args.dataset} 未预处理，请先运行CF-SRec预训练")
        return False

    # 获取数据集信息
    train_loader = dataset_manager.get_data_loader(args.dataset, batch_size=getattr(args, 'batch_size', 32), split='train')
    val_loader = dataset_manager.get_data_loader(args.dataset, batch_size=getattr(args, 'batch_size', 32), split='val')

    if train_loader is None:
        logger.error("❌ 无法创建训练数据加载器")
        return False

    # 创建保存目录
    experiment_name = args.experiment_name or f"collaborative_{args.dataset}"
    save_dir = Path("checkpoints") / experiment_name
    save_dir.mkdir(parents=True, exist_ok=True)

    # 初始化协同模型
    logger.info("🔧 初始化端云协同模型...")

    try:
        from models.collaborative_model import CollaborativeModel

        # 创建协同模型配置
        collaborative_config = {
            'item_num': train_loader.num_items,
            'user_num': train_loader.num_users,
            'device': args.device,
            'collaboration_weight': 0.5,
        }

        # 创建协同模型
        model = CollaborativeModel(collaborative_config)
        model = model.to(args.device)

        logger.info(f"✅ 协同模型初始化完成")
        logger.info(f"📊 数据集统计: {train_loader.num_users} 用户, {train_loader.num_items} 物品")

    except Exception as e:
        logger.error(f"❌ 协同模型初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

    # 开始协同训练
    logger.info("🏃 开始端云协同训练...")

    try:
        num_epochs = min(getattr(args, 'num_epochs', 10), 3)  # 限制epoch用于演示

        # 使用协同模型的训练方法
        model.train_collaborative(
            train_loader=train_loader.dataloader,
            val_loader=val_loader.dataloader if val_loader else None,
            num_epochs=num_epochs
        )

        # 保存协同模型
        model_path = save_dir / "collaborative_model.pth"
        model.save_model(str(model_path))

        logger.info(f"💾 协同模型已保存到: {model_path}")

    except Exception as e:
        logger.error(f"❌ 协同训练过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

    logger.info("🎉 端云协同训练完成！")
    return True


if __name__ == "__main__":
    main()
