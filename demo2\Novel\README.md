# Novel - 基于端云协同的大小模型序列推荐系统

<div align="center">

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
[![PyTorch](https://img.shields.io/badge/PyTorch-2.0+-red.svg)](https://pytorch.org/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Edge-Cloud](https://img.shields.io/badge/Edge--Cloud-Collaboration-orange.svg)](#)
[![Privacy](https://img.shields.io/badge/Privacy-Protected-green.svg)](#)

*解决LLM推荐系统推理延迟和隐私安全问题的端云协同架构*

</div>

## 🎯 核心贡献

### 论文创新点
1. **端云协同架构**: 端侧CF-SRec负责序列建模和快速响应，云端LLM负责深度推荐优化
2. **隐私保护机制**: 原始用户数据保留在端侧，仅传输无法逆向还原的64维用户表示
3. **序列信息增强**: 将CF-SRec提取的用户表示集成到LLM中，增强序列信息与LLM的融合
4. **智能知识蒸馏**: 实现端云深度协同，端云模型持续协同进化

### 解决的关键问题
- ❌ **推理延迟过高**: LLM参数规模庞大，难以满足实时推荐需求
- ❌ **隐私安全担忧**: 用户原始数据上传至云端引发隐私问题
- ❌ **序列建模不足**: 现有方法忽略用户历史行为中的顺序信息

### 技术架构
- **端侧部署**: 轻量级CF-SRec模型，专业序列建模，快速响应
- **云端部署**: 序列知识增强的LLM模型，深度推荐优化
- **安全传输**: 仅传输64维用户表示，确保隐私安全
- **协同进化**: 智能知识蒸馏实现端云模型持续优化

## 🚀 快速开始

### 1. 环境安装

```bash
# 克隆项目
git clone <repository-url>
cd Novel

# 安装依赖
pip install -r requirements.txt

# 验证安装
python -c "import torch; print(f'PyTorch: {torch.__version__}')"
python -c "import transformers; print(f'Transformers: {transformers.__version__}')"
```

### 2. 一键快速开始

```bash
# 一键运行（Linux服务器）
bash quick_start.sh
```

或者使用简化的Python接口：

```python
# 导入主模型
from model import create_recommendation_system

# 创建推荐系统
rec_system = create_recommendation_system()

# 用户历史交互
user_history = [1, 5, 23, 45, 67]

# 生成推荐（端云协同模式）
recommendations = rec_system.recommend(user_history, top_k=10)
print(f"推荐结果: {recommendations}")

# 切换到端侧快速模式
rec_system.switch_mode('edge')
edge_recs = rec_system.recommend(user_history, top_k=10)

# 获取隐私保护报告
privacy_report = rec_system.get_privacy_report()
print(privacy_report)
```

### 3. 完整实验流程

详细的分阶段实验指南请参考：**[QUICKSTART.md](QUICKSTART.md)**

该指南包含：
- 🔬 **阶段一**: 快速验证系统功能
- 📊 **阶段二**: 数据集准备和基础实验
- 🤝 **阶段三**: 端云协同实验
- 🔬 **阶段四**: 知识蒸馏实验
- 📈 **阶段五**: 性能对比分析
- 🎯 **阶段六**: 论文实验复现



### 4. 批量实验（Linux服务器）

```bash
# 运行所有数据集的批量实验
bash run_all_datasets.sh

# 指定GPU运行
bash run_all_datasets.sh 0  # 使用GPU 0

# 后台运行（推荐用于长时间实验）
nohup bash run_all_datasets.sh > batch_log.txt 2>&1 &

# 监控进度
tail -f batch_log.txt

# 查看批量实验结果
ls experiments/batch_results/
```

## 📊 支持的数据集

| 数据集 | 描述 | 规模 | 推荐用途 |
|--------|------|------|----------|
| **ml-100k** | MovieLens 100K评分 | 100K评分 | 快速测试 |
| **Beauty** | Amazon美妆产品评论 | ~200K交互 | 中等规模实验 |
| **Movies_and_TV** | Amazon影视产品评论 | ~1.7M交互 | 大规模实验 |
| **ml-1m** | MovieLens 1M评分 | 1M评分 | 研究实验 |

## 🎛️ 常用命令

### 数据集管理

```bash
# 查看所有可用数据集
python prepare_data.py --list

# 准备单个数据集
python prepare_data.py --dataset Beauty

# 检查数据集状态
python prepare_data.py --status Beauty

# 准备多个数据集
python prepare_data.py --dataset Beauty ml-100k

# 强制重新下载
python prepare_data.py --dataset Beauty --force-download
```

### 实验运行

```bash
# 使用默认配置运行实验
python run_experiment.py --dataset Beauty

# 自定义实验参数
python run_experiment.py \
    --dataset Beauty \
    --experiment-name my_experiment \
    --batch-size 64 \
    --learning-rate 0.001 \
    --num-epochs 20 \
    --device cuda:0

# 使用配置文件运行
python run_experiment.py --config experiments/configs/beauty_experiment.yaml

# 分步骤运行
python run_experiment.py --dataset Beauty --prepare-data-only
python run_experiment.py --config experiments/configs/beauty_experiment.yaml --train-only
python run_experiment.py --config experiments/configs/beauty_experiment.yaml --eval-only --model-path checkpoints/model.pth
```

### 信息查询

```bash
# 列出可用数据集
python run_experiment.py --list-datasets

# 获取数据集信息
python run_experiment.py --dataset-info Beauty

# 列出实验配置
python run_experiment.py --list-configs

# 查看实验状态
python run_experiment.py --experiment-status --config experiments/configs/beauty_experiment.yaml
```

## 📁 项目结构

```
Novel/
├── config/                     # 配置管理
│   ├── collaborative_config.yaml  # 主配置文件
│   └── experiments/           # 实验配置
├── models/                     # 所有模型定义
│   ├── client/                # 端侧模型
│   │   └── cf_srec.py         # CF-SRec序列推荐模型
│   ├── cloud/                 # 云端模型
│   │   └── sequence_enhanced_llm.py # 序列增强LLM
│   ├── collaborative/         # 协同模型
│   │   ├── collaborative_model.py # 基础协同模型
│   │   ├── enhanced_collaborative_model.py # 增强协同模型
│   │   └── edge_cloud_collaborative.py # 端云协同模型
│   ├── knowledge_distillation/ # 知识蒸馏
│   │   └── intelligent_distillation.py # 智能蒸馏
│   └── privacy/               # 隐私保护
│       └── privacy_manager.py # 隐私管理器
├── datasets/                   # 数据集管理
│   ├── dataset_manager.py     # 数据集管理器
│   ├── downloaders.py         # 数据下载器
│   └── data_loader.py         # 数据加载器
├── training/                   # 训练模块
├── utils/                      # 工具函数
│   ├── config_manager.py      # 配置管理器
│   └── result_tracker.py      # 结果跟踪器
├── run_experiment.py          # 主实验脚本
├── prepare_data.py            # 数据准备脚本
├── train_model.py             # 模型训练脚本
├── run_all_datasets.sh        # 批量实验脚本
├── quick_start.sh             # 一键运行脚本
└── requirements.txt           # 依赖列表
```

## 🔧 配置说明

### 预定义配置

- **quick_test.yaml**: 快速测试配置（CPU，3分钟）
- **beauty_experiment.yaml**: Beauty数据集优化配置
- **movielens_experiment.yaml**: MovieLens数据集配置
- **collaborative_config.yaml**: 默认协同配置

### 自定义配置

```yaml
# 示例配置
experiment_name: "my_experiment"
data:
  dataset: "Beauty"
  batch_size: 32
training:
  num_epochs: 20
  learning_rate: 1e-4
small_model:
  hidden_units: 64
large_model:
  llm_model: "llama-3b"
```

## 📈 实验结果

实验结果保存在 `experiments/runs/{experiment_name}/` 目录：

```
experiments/runs/my_experiment/
├── results/
│   ├── latest_results.json      # 完整结果
│   ├── training_history.csv     # 训练历史
│   └── evaluation_metrics.csv   # 评估指标
├── plots/
│   ├── training_curves.png      # 训练曲线
│   └── evaluation_metrics.png   # 指标图表
├── checkpoints/                 # 模型检查点
└── experiment.log              # 详细日志
```

## 🐛 常见问题

### 1. GPU内存不足

```bash
# 使用CPU训练
python run_experiment.py --dataset Beauty --device cpu

# 减少批次大小
python run_experiment.py --dataset Beauty --batch-size 16
```

### 2. 数据下载失败

```bash
# 检查网络连接后重试
python prepare_data.py --dataset Beauty --force-download
```

### 3. 依赖安装问题

```bash
# 更新pip
pip3 install --upgrade pip

# 重新安装依赖
pip3 install -r requirements.txt --force-reinstall
```

### 4. Linux服务器常见问题

```bash
# 检查GPU状态
nvidia-smi

# 查看进程
ps aux | grep python

# 杀死进程
pkill -f "python.*run_experiment"

# 查看磁盘空间
df -h

# 清理临时文件
rm -rf /tmp/pytorch_*
```

## 🎯 最佳实践

### 基本使用
1. **从快速测试开始**: 使用 `quick_test.yaml` 验证环境
2. **选择合适数据集**:
   - 测试: ml-100k
   - 开发: Beauty
   - 研究: Movies_and_TV, ml-1m
3. **保存实验记录**: 使用有意义的实验名称

### Linux服务器使用
1. **会话管理**: 使用 `screen` 或 `tmux` 管理长时间任务
2. **后台运行**: 使用 `nohup` 在后台运行实验
3. **资源监控**:
   - 使用 `nvidia-smi` 监控GPU使用
   - 使用 `htop` 监控CPU和内存
   - 使用 `df -h` 检查磁盘空间
4. **GPU管理**:
   - 指定GPU: `--device cuda:0`
   - 多GPU环境下避免冲突

## 📚 更多信息

- **[完整实验指南](QUICKSTART.md)** - 分阶段实验流程，复现论文结果
- **配置文件**: `config/experiments/` - 各种实验配置选项
- **模型架构**: 查看 `edge/`, `cloud/`, `privacy/` 模块了解技术细节

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个星标！**

</div>
