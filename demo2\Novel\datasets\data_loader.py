"""
推荐系统数据加载器

为训练和评估提供批量数据加载功能。
"""

import json
import logging
import torch
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Iterator
from torch.utils.data import Dataset, DataLoader
import pickle

logger = logging.getLogger(__name__)


class RecommendationDataset(Dataset):
    """推荐系统数据集"""
    
    def __init__(self, data: List[Dict], max_length: int = 128, pad_token: int = 0):
        """
        初始化数据集
        
        Args:
            data: 序列数据列表
            max_length: 最大序列长度
            pad_token: 填充token
        """
        self.data = data
        self.max_length = max_length
        self.pad_token = pad_token
    
    def __len__(self) -> int:
        return len(self.data)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        item = self.data[idx]
        
        # 获取序列
        sequence = item['sequence']
        target_item = item['target_item']
        
        # 填充序列
        padded_sequence = self._pad_sequence(sequence)
        
        # 创建attention mask
        attention_mask = [1] * len(sequence) + [0] * (self.max_length - len(sequence))
        
        return {
            'input_ids': torch.tensor(padded_sequence, dtype=torch.long),
            'attention_mask': torch.tensor(attention_mask, dtype=torch.long),
            'target_item': torch.tensor(target_item, dtype=torch.long),
            'user_id': torch.tensor(item['user_id'], dtype=torch.long),
            'sequence_length': torch.tensor(len(sequence), dtype=torch.long)
        }
    
    def _pad_sequence(self, sequence: List[int]) -> List[int]:
        """填充序列到固定长度"""
        if len(sequence) >= self.max_length:
            return sequence[-self.max_length:]
        else:
            return sequence + [self.pad_token] * (self.max_length - len(sequence))


class RecommendationDataLoader:
    """推荐系统数据加载器"""
    
    def __init__(self, data_path: str, batch_size: int = 32, split: str = 'train',
                 max_length: int = 128, num_workers: int = 0, shuffle: bool = None):
        """
        初始化数据加载器
        
        Args:
            data_path: 数据路径
            batch_size: 批次大小
            split: 数据分割 ('train', 'val', 'test')
            max_length: 最大序列长度
            num_workers: 数据加载进程数
            shuffle: 是否打乱数据
        """
        self.data_path = Path(data_path)
        self.batch_size = batch_size
        self.split = split
        self.max_length = max_length
        self.num_workers = num_workers
        
        # 默认训练集打乱，验证和测试集不打乱
        if shuffle is None:
            self.shuffle = (split == 'train')
        else:
            self.shuffle = shuffle
        
        # 加载数据
        self.data = self._load_data()
        self.dataset = RecommendationDataset(self.data, max_length)
        
        # 创建DataLoader
        self.dataloader = DataLoader(
            self.dataset,
            batch_size=batch_size,
            shuffle=self.shuffle,
            num_workers=num_workers,
            pin_memory=torch.cuda.is_available()
        )
        
        # 加载编码器和统计信息
        self.encoders = self._load_encoders()
        self.statistics = self._load_statistics()

        # 计算数据集统计信息
        self._num_users = len(set(item['user_id'] for item in self.data))
        self._num_items = max(max(item['sequence']) for item in self.data if item['sequence']) + 1
        self._num_interactions = len(self.data)

        logger.info(f"Initialized {split} data loader with {len(self.data)} samples")
        logger.info(f"Dataset stats: {self._num_users} users, {self._num_items} items, {self._num_interactions} interactions")
    
    def _load_data(self) -> List[Dict]:
        """加载数据"""
        data_file = self.data_path / f"{self.split}.json"

        if not data_file.exists():
            raise FileNotFoundError(f"Data file not found: {data_file}")

        data = []
        with open(data_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f):
                line = line.strip()
                if not line:
                    continue
                try:
                    item = json.loads(line)
                    data.append(item)
                except json.JSONDecodeError as e:
                    logger.warning(f"Skipping invalid JSON at line {line_num + 1}: {e}")
                    continue

        logger.info(f"Loaded {len(data)} samples from {data_file}")
        return data
    
    def _load_encoders(self) -> Optional[Dict]:
        """加载编码器"""
        encoder_file = self.data_path / "encoders.pkl"
        
        if not encoder_file.exists():
            logger.warning(f"Encoder file not found: {encoder_file}")
            return None
        
        with open(encoder_file, 'rb') as f:
            encoders = pickle.load(f)
        
        return encoders
    
    def _load_statistics(self) -> Optional[Dict]:
        """加载统计信息"""
        stats_file = self.data_path / "statistics.json"
        
        if not stats_file.exists():
            logger.warning(f"Statistics file not found: {stats_file}")
            return None
        
        with open(stats_file, 'r', encoding='utf-8') as f:
            statistics = json.load(f)
        
        return statistics
    
    def __iter__(self) -> Iterator[Dict[str, torch.Tensor]]:
        """迭代器接口"""
        return iter(self.dataloader)
    
    def __len__(self) -> int:
        """返回批次数量"""
        return len(self.dataloader)
    
    @property
    def num_users(self) -> int:
        """用户数量"""
        if self.statistics:
            return self.statistics.get('num_users', 0)
        return 0
    
    @property
    def num_items(self) -> int:
        """物品数量"""
        if self.statistics:
            return self.statistics.get('num_items', 0)
        return 0
    
    @property
    def vocab_size(self) -> int:
        """词汇表大小（物品数量+1，包含padding token）"""
        return self.num_items + 1
    
    def get_user_encoder(self) -> Optional[Dict]:
        """获取用户编码器"""
        if self.encoders:
            return self.encoders.get('user_encoder')
        return None
    
    def get_item_encoder(self) -> Optional[Dict]:
        """获取物品编码器"""
        if self.encoders:
            return self.encoders.get('item_encoder')
        return None
    
    def get_user_decoder(self) -> Optional[Dict]:
        """获取用户解码器"""
        if self.encoders:
            return self.encoders.get('user_decoder')
        return None
    
    def get_item_decoder(self) -> Optional[Dict]:
        """获取物品解码器"""
        if self.encoders:
            return self.encoders.get('item_decoder')
        return None
    
    def decode_items(self, item_ids: List[int]) -> List[str]:
        """解码物品ID为原始ID"""
        item_decoder = self.get_item_decoder()
        if not item_decoder:
            return [str(item_id) for item_id in item_ids]
        
        return [item_decoder.get(item_id, f"unknown_{item_id}") for item_id in item_ids]
    
    def decode_users(self, user_ids: List[int]) -> List[str]:
        """解码用户ID为原始ID"""
        user_decoder = self.get_user_decoder()
        if not user_decoder:
            return [str(user_id) for user_id in user_ids]
        
        return [user_decoder.get(user_id, f"unknown_{user_id}") for user_id in user_ids]
    
    def get_sample_batch(self) -> Dict[str, torch.Tensor]:
        """获取一个样本批次用于测试"""
        return next(iter(self.dataloader))
    
    def get_statistics_summary(self) -> str:
        """获取数据集统计信息摘要"""
        if not self.statistics:
            return "No statistics available"
        
        summary = f"""
Dataset Statistics:
- Users: {self.statistics.get('num_users', 'N/A')}
- Items: {self.statistics.get('num_items', 'N/A')}
- Interactions: {self.statistics.get('num_interactions', 'N/A')}
- Sequences: {self.statistics.get('num_sequences', 'N/A')}
- Avg Sequence Length: {self.statistics.get('avg_sequence_length', 'N/A'):.2f}
- Split: {self.split}
- Batch Size: {self.batch_size}
- Max Length: {self.max_length}
        """.strip()
        
        return summary

    @property
    def num_users(self):
        """用户数量"""
        return self._num_users

    @property
    def num_items(self):
        """物品数量"""
        return self._num_items

    @property
    def num_interactions(self):
        """交互数量"""
        return self._num_interactions


def create_data_loaders(data_path: str, batch_size: int = 32, max_length: int = 128,
                       num_workers: int = 0) -> Tuple[RecommendationDataLoader, 
                                                     RecommendationDataLoader, 
                                                     RecommendationDataLoader]:
    """
    创建训练、验证和测试数据加载器
    
    Args:
        data_path: 数据路径
        batch_size: 批次大小
        max_length: 最大序列长度
        num_workers: 数据加载进程数
        
    Returns:
        (train_loader, val_loader, test_loader)
    """
    train_loader = RecommendationDataLoader(
        data_path=data_path,
        batch_size=batch_size,
        split='train',
        max_length=max_length,
        num_workers=num_workers,
        shuffle=True
    )
    
    val_loader = RecommendationDataLoader(
        data_path=data_path,
        batch_size=batch_size,
        split='val',
        max_length=max_length,
        num_workers=num_workers,
        shuffle=False
    )
    
    test_loader = RecommendationDataLoader(
        data_path=data_path,
        batch_size=batch_size,
        split='test',
        max_length=max_length,
        num_workers=num_workers,
        shuffle=False
    )
    
    return train_loader, val_loader, test_loader
