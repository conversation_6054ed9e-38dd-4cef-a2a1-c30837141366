"""
LLMRec主模型 - 直接复用LLM-SRec实现

移植自：LLM-SRec/models/seqllm_model.py
简化并适配Novel项目的数据格式
"""

import torch
import torch.nn as nn
import numpy as np
import random
import logging
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path

from .llm4rec import llm4rec
from .cf_srec import CFSRec

logger = logging.getLogger(__name__)


class llmrec_model(nn.Module):
    """
    LLMRec主模型 - 直接复用LLM-SRec的实现
    
    组合CF-SRec和LLM4Rec，实现：
    - 序列推荐模型作为监督信号
    - LLM进行表示学习和对齐
    - 文本构造和特殊token处理
    """
    
    def __init__(self, args, dataset_info, item_text_dict=None):
        """
        初始化LLMRec模型
        
        Args:
            args: 全局配置参数
            dataset_info: 数据集信息
            item_text_dict: 物品文本字典
        """
        super().__init__()
        self.args = args
        self.device = args.device
        self.dataset_info = dataset_info
        self.item_text_dict = item_text_dict or {}
        
        # 数据集配置
        self.item_num = dataset_info['item_num']
        self.user_num = dataset_info['user_num']
        
        # 初始化CF-SRec模型（冻结）
        self._init_cf_srec()
        
        # 初始化LLM组件
        self._init_llm()
        
        # 初始化物品嵌入投影
        self._init_item_projection()
        
        logger.info("LLMRec model initialized")
    
    def _init_cf_srec(self):
        """初始化CF-SRec模型"""
        # 创建CF-SRec配置（匹配预训练模型）
        cf_srec_config = {
            'item_num': self.item_num,
            'hidden_size': 128,           # 匹配预训练模型
            'num_hidden_layers': 4,       # 匹配预训练模型
            'num_attention_heads': 1,
            'dropout_prob': 0.2,
            'max_seq_length': 128,        # 匹配预训练模型
            'user_repr_dim': 128,
            'device': self.device
        }
        
        # 创建CF-SRec模型
        self.recsys = type('RecSys', (), {})()
        self.recsys.model = CFSRec(cf_srec_config)
        
        # 冻结CF-SRec参数
        for param in self.recsys.model.parameters():
            param.requires_grad = False
        
        logger.info("CF-SRec model initialized and frozen")
    
    def _init_llm(self):
        """初始化LLM组件"""
        llm_model = getattr(self.args, 'llm_model', 'llama-3b')
        self.llm = llm4rec(
            device=self.device,
            llm_model=llm_model,
            args=self.args
        )
        
        logger.info(f"LLM component initialized: {llm_model}")
    
    def _init_item_projection(self):
        """初始化物品嵌入投影层"""
        cf_hidden_size = 128  # 匹配CF-SRec的实际配置
        llm_hidden_size = self.llm.hidden_size
        
        self.item_emb_proj = nn.Linear(cf_hidden_size, llm_hidden_size)
        
        logger.info(f"Item embedding projection: {cf_hidden_size} -> {llm_hidden_size}")
    
    def get_item_emb(self, item_ids):
        """获取物品嵌入"""
        # 从CF-SRec获取物品嵌入
        item_emb = self.recsys.model.item_emb(torch.LongTensor(item_ids).to(self.device))
        return item_emb
    
    def find_item_text_single(self, item_id, title_flag=True, description_flag=False):
        """
        获取单个物品的文本描述
        
        Args:
            item_id: 物品ID
            title_flag: 是否包含标题
            description_flag: 是否包含描述
        """
        if item_id in self.item_text_dict:
            item_info = self.item_text_dict[item_id]
            text_parts = []
            
            if title_flag and 'title' in item_info:
                text_parts.append(item_info['title'])
            
            if description_flag and 'description' in item_info:
                text_parts.append(item_info['description'])
            
            return ' '.join(text_parts) if text_parts else f"Item {item_id}"
        else:
            return f"Item {item_id}"
    
    def make_interact_text(self, interact_ids, interact_max_num, user_id):
        """
        构造用户历史交互的文本表示
        
        Args:
            interact_ids: 历史交互物品ID列表
            interact_max_num: 最大交互数量
            user_id: 用户ID
        """
        if isinstance(interact_max_num, str) and interact_max_num == 'all':
            selected_ids = interact_ids
        else:
            # 取最近的N个交互
            selected_ids = interact_ids[-int(interact_max_num):]
        
        interact_text_parts = []
        for i, item_id in enumerate(selected_ids):
            item_title = self.find_item_text_single(item_id, title_flag=True, description_flag=False)
            interact_text_parts.append(f"Item No.{i+1}, {item_title}[HistoryEmb]")
        
        interact_text = ",".join(interact_text_parts)
        return interact_text, selected_ids
    
    def make_candidate_text(self, interact_ids, candidate_num, target_item_id, target_item_title, task='RecTask'):
        """
        构造候选物品的文本表示
        
        Args:
            interact_ids: 用户历史交互ID列表
            candidate_num: 候选物品数量
            target_item_id: 目标物品ID（正样本）
            target_item_title: 目标物品标题
            task: 任务类型
        """
        # 负采样：排除历史交互物品
        interact_set = set(interact_ids)
        neg_candidates = []
        
        while len(neg_candidates) < candidate_num - 1:
            neg_id = random.randint(1, self.item_num)
            if neg_id not in interact_set and neg_id != target_item_id:
                neg_candidates.append(neg_id)
        
        # 构建候选物品列表：正样本在第一位
        candidate_ids = [target_item_id] + neg_candidates
        
        # 构建文本表示
        candidate_text = []
        for cand_id in candidate_ids:
            if cand_id == target_item_id:
                title = target_item_title
            else:
                title = self.find_item_text_single(cand_id, title_flag=True, description_flag=False)
            
            text = f'The item title and item embedding are as follows: {title}[HistoryEmb], then generate item representation token:[ItemOut]'
            candidate_text.append(text)
        
        return candidate_text, candidate_ids
    
    def forward(self, data, optimizer=None, batch_iter=None, mode='phase2'):
        """
        模型前向传播统一入口
        
        Args:
            data: 输入数据
            optimizer: 优化器
            batch_iter: 训练进度信息
            mode: 运行模式
        """
        if mode == 'phase2':
            return self.pre_train_phase2(data, optimizer, batch_iter)
        else:
            raise NotImplementedError(f"Mode {mode} not implemented")
    
    def pre_train_phase2(self, data, optimizer, batch_iter):
        """
        LLM-SRec核心训练循环 - 第二阶段预训练
        
        Args:
            data: 批次训练数据 (u, seq, pos, neg)
            optimizer: 优化器
            batch_iter: 训练进度信息 (epoch, total_epoch, step, total_step)
        """
        epoch, total_epoch, step, total_step = batch_iter
        
        # 清零梯度
        optimizer.zero_grad()
        
        # 解包数据
        u, seq, pos, neg = data
        
        # 从CF-SRec获取用户表示（监督信号）
        with torch.no_grad():
            # 将序列转换为tensor
            seq_tensor = torch.tensor(seq, dtype=torch.long, device=self.device)
            cf_output = self.recsys.model(seq_tensor)
            log_emb = cf_output['user_repr']  # 使用用户表示作为监督信号
        
        # 构造训练样本
        text_input = []
        candidates_pos = []
        interact_embs = []
        candidate_embs_pos = []
        
        # 逐样本构造数据
        for i in range(len(u)):
            # 获取目标物品
            target_item_id = pos[i][-1]
            target_item_title = self.find_item_text_single(target_item_id, title_flag=True, description_flag=False)
            
            # 构造用户历史交互文本
            interact_text, interact_ids = self.make_interact_text(seq[i][seq[i]>0], 10, u[i])
            
            # 构造候选物品文本
            candidate_num = 4
            candidate_text, candidate_ids = self.make_candidate_text(
                seq[i][seq[i]>0], candidate_num, target_item_id, target_item_title, task='RecTask'
            )
            
            # 构造用户输入文本
            input_text = 'This user has made a series of purchases in the following order: '
            input_text += interact_text
            input_text += ". Based on this sequence of purchases, generate user representation token:[UserOut]"
            
            text_input.append(input_text)
            candidates_pos.extend(candidate_text)
            
            # 准备嵌入
            interact_embs.append(self.item_emb_proj(self.get_item_emb(interact_ids)))
            candidate_embs_pos.append(self.item_emb_proj(self.get_item_emb(candidate_ids)))
        
        # 整合批次数据
        candidate_embs = torch.cat(candidate_embs_pos)
        
        # 构造LLM训练样本
        samples = {
            'text_input': text_input,
            'log_emb': log_emb,
            'candidates_pos': candidates_pos,
            'interact': interact_embs,
            'candidate_embs': candidate_embs,
        }
        
        # LLM前向传播和损失计算
        loss, rec_loss, match_loss = self.llm(samples, mode=0)
        
        # 输出训练日志
        log_interval = getattr(self.args, 'log_interval', 10)
        if log_interval > 0 and (step % log_interval) == 0:
            logger.info(f"LLMRec model loss in epoch {epoch}/{total_epoch} iteration {step}/{total_step}: {rec_loss}")
            logger.info(f"LLMRec model Matching loss in epoch {epoch}/{total_epoch} iteration {step}/{total_step}: {match_loss}")
        
        # 反向传播
        loss.backward()
        
        # 特殊硬件优化
        if getattr(self.args, 'nn_parameter', False):
            import habana_frameworks.torch.core as htcore
            htcore.mark_step()
        
        optimizer.step()
        
        if getattr(self.args, 'nn_parameter', False):
            import habana_frameworks.torch.core as htcore
            htcore.mark_step()
    
    def save_model(self, args, epoch2=None):
        """保存模型"""
        save_dir = Path(args.save_dir)
        save_dir.mkdir(parents=True, exist_ok=True)
        
        if epoch2 is not None:
            model_path = save_dir / f"llm_model_epoch_{epoch2}.pth"
        else:
            model_path = save_dir / "llm_model.pth"
        
        torch.save({
            'llm_state_dict': self.llm.state_dict(),
            'item_proj_state_dict': self.item_emb_proj.state_dict(),
            'args': args,
        }, model_path)
        
        logger.info(f"Model saved to {model_path}")
    
    def load_model(self, model_path):
        """加载模型"""
        checkpoint = torch.load(model_path, map_location=self.device)
        
        self.llm.load_state_dict(checkpoint['llm_state_dict'])
        self.item_emb_proj.load_state_dict(checkpoint['item_proj_state_dict'])
        
        logger.info(f"Model loaded from {model_path}")
