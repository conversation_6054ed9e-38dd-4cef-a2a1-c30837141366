"""
端云协同通信模块

本模块实现端侧和云端之间的安全通信机制：
1. 异步通信：支持高并发的端云数据交换
2. 安全传输：加密用户表示向量的传输
3. 负载均衡：智能分配云端计算资源
4. 容错机制：网络异常时的重试和降级策略

核心功能：
- 安全数据传输：端到云的加密通信
- 批量处理：支持批量用户表示传输
- 实时响应：低延迟的推荐服务
- 监控统计：通信性能和质量监控
"""

import asyncio
import aiohttp
import json
import time
import logging
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass, asdict
import numpy as np
import torch
from concurrent.futures import ThreadPoolExecutor
import ssl
import certifi

from privacy import EdgeCloudPrivacyProtector, PrivacyConfig


@dataclass
class CommunicationConfig:
    """通信配置"""
    # 服务器配置
    cloud_server_url: str = "https://cloud-llm-server.example.com"
    api_key: str = "your-api-key-here"
    timeout: float = 30.0
    
    # 重试配置
    max_retries: int = 3
    retry_delay: float = 1.0
    backoff_factor: float = 2.0
    
    # 批处理配置
    batch_size: int = 32
    batch_timeout: float = 5.0
    
    # 性能配置
    max_concurrent_requests: int = 100
    connection_pool_size: int = 50
    
    # 安全配置
    verify_ssl: bool = True
    use_compression: bool = True


@dataclass
class RequestMetrics:
    """请求指标"""
    request_id: str
    user_id: str
    start_time: float
    end_time: float
    success: bool
    error_message: Optional[str] = None
    response_size: int = 0
    retry_count: int = 0


class EdgeCloudCommunicator:
    """
    端云协同通信器
    
    负责端侧和云端之间的安全、高效通信：
    1. 发送加密的用户表示到云端
    2. 接收云端的推荐结果
    3. 处理通信异常和重试
    4. 监控通信性能
    """
    
    def __init__(self, config: CommunicationConfig, privacy_config: Optional[PrivacyConfig] = None):
        """
        初始化通信器
        
        Args:
            config: 通信配置
            privacy_config: 隐私保护配置
        """
        self.config = config
        self.privacy_protector = EdgeCloudPrivacyProtector(
            privacy_config or PrivacyConfig()
        )
        
        # 通信状态
        self.session = None
        self.is_connected = False
        self.request_queue = asyncio.Queue()
        self.batch_queue = []
        self.batch_timer = None
        
        # 性能监控
        self.metrics = []
        self.performance_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0,
            'total_data_sent': 0,
            'total_data_received': 0
        }
        
        # 并发控制
        self.semaphore = asyncio.Semaphore(config.max_concurrent_requests)
        self.executor = ThreadPoolExecutor(max_workers=10)
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
    async def initialize(self):
        """初始化通信连接"""
        # 创建SSL上下文
        ssl_context = ssl.create_default_context(cafile=certifi.where()) if self.config.verify_ssl else False
        
        # 创建连接器
        connector = aiohttp.TCPConnector(
            limit=self.config.connection_pool_size,
            ssl=ssl_context,
            enable_cleanup_closed=True
        )
        
        # 创建会话
        timeout = aiohttp.ClientTimeout(total=self.config.timeout)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                'Authorization': f'Bearer {self.config.api_key}',
                'Content-Type': 'application/json',
                'User-Agent': 'EdgeCloudRecommendationSystem/1.0'
            }
        )
        
        self.is_connected = True
        self.logger.info("端云通信连接已建立")
    
    async def close(self):
        """关闭通信连接"""
        if self.session:
            await self.session.close()
        
        if self.batch_timer:
            self.batch_timer.cancel()
        
        self.executor.shutdown(wait=True)
        self.is_connected = False
        self.logger.info("端云通信连接已关闭")
    
    async def send_user_representation(
        self, 
        user_repr: torch.Tensor,
        user_id: str,
        request_recommendations: bool = True,
        num_recommendations: int = 10
    ) -> Dict[str, Any]:
        """
        发送用户表示到云端
        
        Args:
            user_repr: 用户表示向量
            user_id: 用户ID
            request_recommendations: 是否请求推荐
            num_recommendations: 推荐数量
            
        Returns:
            云端响应结果
        """
        if not self.is_connected:
            await self.initialize()
        
        # 生成请求ID
        request_id = f"{user_id}_{int(time.time() * 1000)}"
        start_time = time.time()
        
        try:
            # 加密用户表示
            encrypted_package, risk_assessment = self.privacy_protector.protect_and_transmit(
                user_repr, user_id
            )
            
            # 构建请求数据
            request_data = {
                'request_id': request_id,
                'user_id': user_id,
                'encrypted_user_representation': encrypted_package,
                'privacy_risk_assessment': risk_assessment,
                'request_recommendations': request_recommendations,
                'num_recommendations': num_recommendations,
                'timestamp': time.time()
            }
            
            # 发送请求
            response_data = await self._send_request_with_retry(request_data)
            
            # 记录成功指标
            self._record_metrics(
                request_id, user_id, start_time, time.time(), 
                True, None, len(json.dumps(response_data))
            )
            
            return response_data
            
        except Exception as e:
            # 记录失败指标
            self._record_metrics(
                request_id, user_id, start_time, time.time(), 
                False, str(e), 0
            )
            
            self.logger.error(f"发送用户表示失败: {e}")
            raise
    
    async def batch_send_user_representations(
        self, 
        user_data: List[Tuple[torch.Tensor, str]],
        request_recommendations: bool = True,
        num_recommendations: int = 10
    ) -> List[Dict[str, Any]]:
        """
        批量发送用户表示
        
        Args:
            user_data: [(用户表示, 用户ID), ...]
            request_recommendations: 是否请求推荐
            num_recommendations: 推荐数量
            
        Returns:
            批量响应结果
        """
        if not self.is_connected:
            await self.initialize()
        
        # 分批处理
        results = []
        batch_size = self.config.batch_size
        
        for i in range(0, len(user_data), batch_size):
            batch = user_data[i:i + batch_size]
            
            # 并发发送批次
            tasks = []
            for user_repr, user_id in batch:
                task = self.send_user_representation(
                    user_repr, user_id, request_recommendations, num_recommendations
                )
                tasks.append(task)
            
            # 等待批次完成
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常结果
            for result in batch_results:
                if isinstance(result, Exception):
                    results.append({
                        'success': False,
                        'error': str(result),
                        'timestamp': time.time()
                    })
                else:
                    results.append(result)
        
        return results
    
    async def _send_request_with_retry(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        带重试机制的请求发送
        
        Args:
            request_data: 请求数据
            
        Returns:
            响应数据
        """
        last_exception = None
        retry_delay = self.config.retry_delay
        
        for attempt in range(self.config.max_retries + 1):
            try:
                async with self.semaphore:  # 并发控制
                    response_data = await self._send_single_request(request_data)
                    return response_data
                    
            except Exception as e:
                last_exception = e
                self.logger.warning(f"请求失败 (尝试 {attempt + 1}/{self.config.max_retries + 1}): {e}")
                
                if attempt < self.config.max_retries:
                    await asyncio.sleep(retry_delay)
                    retry_delay *= self.config.backoff_factor
        
        # 所有重试都失败
        raise last_exception
    
    async def _send_single_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送单个请求
        
        Args:
            request_data: 请求数据
            
        Returns:
            响应数据
        """
        # 序列化请求数据
        json_data = json.dumps(request_data)
        
        # 压缩数据（如果启用）
        if self.config.use_compression:
            headers = {'Content-Encoding': 'gzip'}
            import gzip
            data = gzip.compress(json_data.encode('utf-8'))
        else:
            headers = {}
            data = json_data.encode('utf-8')
        
        # 发送HTTP请求
        async with self.session.post(
            f"{self.config.cloud_server_url}/api/v1/recommend",
            data=data,
            headers=headers
        ) as response:
            
            # 检查响应状态
            if response.status != 200:
                error_text = await response.text()
                raise aiohttp.ClientResponseError(
                    request_info=response.request_info,
                    history=response.history,
                    status=response.status,
                    message=f"HTTP {response.status}: {error_text}"
                )
            
            # 解析响应
            response_data = await response.json()
            
            # 更新统计信息
            self.performance_stats['total_data_sent'] += len(data)
            self.performance_stats['total_data_received'] += len(await response.read())
            
            return response_data
    
    def _record_metrics(
        self, 
        request_id: str, 
        user_id: str, 
        start_time: float, 
        end_time: float,
        success: bool, 
        error_message: Optional[str], 
        response_size: int,
        retry_count: int = 0
    ):
        """记录请求指标"""
        metrics = RequestMetrics(
            request_id=request_id,
            user_id=user_id,
            start_time=start_time,
            end_time=end_time,
            success=success,
            error_message=error_message,
            response_size=response_size,
            retry_count=retry_count
        )
        
        self.metrics.append(metrics)
        
        # 更新性能统计
        self.performance_stats['total_requests'] += 1
        if success:
            self.performance_stats['successful_requests'] += 1
        else:
            self.performance_stats['failed_requests'] += 1
        
        # 更新平均响应时间
        response_time = end_time - start_time
        total_requests = self.performance_stats['total_requests']
        current_avg = self.performance_stats['average_response_time']
        self.performance_stats['average_response_time'] = (
            (current_avg * (total_requests - 1) + response_time) / total_requests
        )
    
    def get_performance_statistics(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            性能统计数据
        """
        success_rate = (
            self.performance_stats['successful_requests'] / 
            max(self.performance_stats['total_requests'], 1)
        )
        
        return {
            **self.performance_stats,
            'success_rate': success_rate,
            'connection_status': 'connected' if self.is_connected else 'disconnected',
            'metrics_count': len(self.metrics)
        }
    
    def get_recent_metrics(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取最近的请求指标
        
        Args:
            limit: 返回的指标数量限制
            
        Returns:
            最近的请求指标列表
        """
        recent_metrics = self.metrics[-limit:] if self.metrics else []
        return [asdict(metric) for metric in recent_metrics]
    
    def clear_metrics(self):
        """清空指标数据"""
        self.metrics.clear()
        self.performance_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0,
            'total_data_sent': 0,
            'total_data_received': 0
        }


async def create_communicator(
    cloud_server_url: str,
    api_key: str,
    privacy_level: str = 'high'
) -> EdgeCloudCommunicator:
    """
    创建端云通信器的便捷函数
    
    Args:
        cloud_server_url: 云端服务器URL
        api_key: API密钥
        privacy_level: 隐私保护级别
        
    Returns:
        初始化后的通信器
    """
    comm_config = CommunicationConfig(
        cloud_server_url=cloud_server_url,
        api_key=api_key
    )
    
    privacy_config = PrivacyConfig(
        user_repr_dim=64,
        privacy_level=privacy_level,
        enable_encryption=True
    )
    
    communicator = EdgeCloudCommunicator(comm_config, privacy_config)
    await communicator.initialize()
    
    return communicator
