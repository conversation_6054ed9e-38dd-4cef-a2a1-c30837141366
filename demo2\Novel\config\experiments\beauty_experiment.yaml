# Beauty数据集实验配置
# 针对Amazon Beauty产品推荐的优化配置

experiment_name: "beauty_experiment"
seed: 42
device: "cuda:0"
log_level: "INFO"

# ==================== 数据配置 ====================
data:
  dataset: "Beauty"
  data_dir: "./data"
  raw_data_dir: "./data/raw"
  processed_data_dir: "./data/processed"
  
  # Beauty数据集特定参数
  min_interactions: 5
  max_sequence_length: 64  # Beauty数据集序列较短
  train_ratio: 0.8
  val_ratio: 0.1
  test_ratio: 0.1
  
  # 数据增强
  data_augmentation: true
  augmentation_ratio: 0.15  # 增加数据增强比例

# ==================== 小模型配置 ====================
small_model:
  model_type: "cf_srec"
  
  # 针对Beauty数据集的模型架构
  item_num: 5000  # Beauty数据集物品数量较少
  hidden_units: 32  # 减少隐藏单元
  num_blocks: 2
  num_heads: 1
  dropout_rate: 0.3  # 增加dropout防止过拟合
  max_sequence_length: 64
  
  # LoRA配置
  use_lora: true
  lora_r: 4  # 减少LoRA参数
  lora_alpha: 8
  lora_dropout: 0.1

# ==================== 大模型配置 ====================
large_model:
  model_type: "llm_recommender"
  
  # 使用较小的LLM
  llm_model: "llama-3b"
  load_in_8bit: true
  load_in_4bit: false
  
  # LLM生成参数
  max_length: 256  # 减少生成长度
  temperature: 0.8
  top_p: 0.9
  top_k: 40
  
  # LoRA微调配置
  use_lora: true
  lora_r: 8
  lora_alpha: 16
  lora_dropout: 0.1
  lora_target_modules: ["q_proj", "v_proj"]

# ==================== 协同机制配置 ====================
collaboration:
  collaboration_method: "knowledge_distillation"
  
  # 知识蒸馏参数
  distillation_temperature: 3.0
  distillation_weight: 0.3
  
  # 特征对齐参数
  alignment_weight: 0.15
  alignment_method: "cosine"
  
  # 联合训练参数
  joint_training: true
  small_model_weight: 0.4
  large_model_weight: 0.3
  collaborative_weight: 0.3

# ==================== 训练配置 ====================
training:
  # 基础训练参数
  num_epochs: 30  # 减少训练轮数
  batch_size: 64  # 增加批次大小
  learning_rate: 5e-4  # 增加学习率
  weight_decay: 1e-4
  gradient_clip: 0.5
  
  # 学习率调度
  scheduler: "cosine"
  warmup_steps: 500
  warmup_ratio: 0.1
  
  # 早停参数
  patience: 8
  min_delta: 1e-4
  
  # 输出配置
  output_dir: "./checkpoints/beauty"
  save_steps: 500
  eval_steps: 250
  logging_steps: 50
  
  # 实验跟踪
  use_wandb: false
  wandb_project: "llm-srec-beauty"
  use_tensorboard: true

# ==================== 评估配置 ====================
evaluation:
  # 评估指标
  metrics: ["ndcg@5", "ndcg@10", "hit_rate@5", "hit_rate@10", "recall@5", "recall@10", "mrr@5", "mrr@10"]
  
  # 推荐参数
  top_k_list: [5, 10, 20]
  
  # 评估频率
  eval_during_training: true
  eval_steps: 250

# ==================== 推理配置 ====================
inference:
  batch_size: 128
  top_k: 10
  output_format: "json"
  include_scores: true
  include_explanations: true  # Beauty产品需要解释

# ==================== 优化配置 ====================
optimization:
  # 混合精度训练
  fp16: true
  bf16: false
  
  # 梯度累积
  gradient_accumulation_steps: 1
  
  # 数据并行
  dataloader_num_workers: 2
  dataloader_pin_memory: true
  
  # 模型并行
  model_parallel: false
  device_map: "auto"

# ==================== 损失函数配置 ====================
loss:
  recommendation_loss: "cross_entropy"
  
  # 辅助损失权重
  small_model_loss_weight: 0.4
  large_model_loss_weight: 0.3
  collaborative_loss_weight: 0.3
  distillation_loss_weight: 0.3
  alignment_loss_weight: 0.15
  
  # 正则化
  l1_regularization: 0.0
  l2_regularization: 1e-4

# ==================== 数据增强配置 ====================
data_augmentation:
  # 序列增强
  sequence_augmentation: true
  crop_ratio: 0.3
  mask_ratio: 0.15
  reorder_ratio: 0.1
  
  # 负采样
  negative_sampling: true
  negative_sample_ratio: 2.0  # 增加负样本比例
  negative_sampling_strategy: "popularity"

# ==================== 实验配置 ====================
experiment:
  # 消融实验
  ablation_study: false
  ablation_components: ["small_model", "large_model", "distillation"]
  
  # 超参数搜索
  hyperparameter_search: false
  search_space:
    learning_rate: [1e-4, 5e-4, 1e-3]
    batch_size: [32, 64, 128]
    hidden_units: [16, 32, 64]
    distillation_temperature: [2.0, 3.0, 4.0]
  
  # 多种子实验
  multi_seed: false
  seeds: [42, 123, 456]
