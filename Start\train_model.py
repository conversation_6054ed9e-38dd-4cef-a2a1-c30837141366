import os
import torch
import random
import time
import os
import sys

from tqdm import tqdm

import torch.multiprocessing as mp
from torch.utils.data import DataLoader
from torch.utils.data.distributed import DistributedSampler
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.distributed import init_process_group, destroy_process_group
from torch.optim.lr_scheduler import LambdaLR

from models.seqllm_model import *
from SeqRec.sasrec.utils import data_partition, SeqDataset, SeqDataset_Inference, SeqDataset_Validation
from utils import create_dir




def setup_ddp(rank, world_size, args):
    os.environ['MASTER_ADDR'] = 'localhost'
    os.environ['MASTER_PORT'] = '12355'
    os.environ["ID"] = str(rank)
    if hasattr(args.device, 'type') and args.device.type == 'hpu':
        import habana_frameworks.torch.distributed.hccl
        init_process_group(backend="hccl", rank=rank, world_size=world_size)
    else:
        init_process_group(backend="nccl", rank=rank, world_size=world_size)
        torch.cuda.set_device(rank)
    # htcore.set_device(rank)

def train_model(args):
    print('LLMRec strat train\n')
    if args.multi_gpu:
        world_size = args.world_size
        mp.spawn(train_model_,
             args=(world_size,args),
             nprocs=world_size,
             join=True)
    else:
        train_model_(0, 0, args)

def inference(args):
    print('LLMRec start inference\n')
    if args.multi_gpu:
        world_size = args.world_size
        mp.spawn(inference_,
             args=(world_size,args),
             nprocs=world_size,
             join=True)
    else:
        inference_(0,0,args)
  

def train_model_(rank,world_size,args):
    if args.multi_gpu:
        setup_ddp(rank, world_size, args)
        if args.device == 'hpu':
            args.device = torch.device('hpu')
        else:
            args.device = 'cuda:' + str(rank)
    random.seed(0)

    model = llmrec_model(args).to(args.device)

    dataset = data_partition(args.rec_pre_trained_data, args, path=f'./SeqRec/data_{args.rec_pre_trained_data}/{args.rec_pre_trained_data}')
    [user_train, user_valid, user_test, usernum, itemnum, eval_set] = dataset
    print('user num:', usernum, 'item num:', itemnum)
    num_batch = len(user_train) // args.batch_size
    cc = 0.0
    for u in user_train:
        cc += len(user_train[u])
    print('average sequence length: %.2f' % (cc / len(user_train)))
    # Init Dataloader, Model, Optimizer
    train_data_set = SeqDataset(user_train, len(user_train.keys()), itemnum, args.maxlen)
    
    
    if args.multi_gpu:
        train_data_loader = DataLoader(
            train_data_set,
            batch_size=args.batch_size,
            sampler=DistributedSampler(train_data_set, shuffle=True),
            pin_memory=True,
            num_workers=4,
        )
        # Wrap model once for DDP
        model = DDP(
            model,
            device_ids=[rank],
            output_device=rank,
            static_graph=True,
            broadcast_buffers=False,
        )
    else:
        train_data_loader = DataLoader(train_data_set, batch_size = args.batch_size, pin_memory=True, shuffle=True, num_workers=4)
    # 取出真实模型（DDP 包装则使用 .module）以读取/写入统计指标
    core_model = model.module if isinstance(model, DDP) else model
    adam_optimizer = torch.optim.Adam(model.parameters(), lr=args.stage2_lr, betas=(0.9, 0.98))
    scheduler = LambdaLR(adam_optimizer, lr_lambda = lambda epoch: 0.95 ** epoch)
    epoch_start_idx = 1
    T = 0.0
    perform = 0
    best_perform = 0
    early_stop = 0
    early_thres = 5
    t0 = time.time()

    # 断点续训：加载checkpoint
    if hasattr(args, 'resume') and args.resume and os.path.exists(args.resume):
        if rank == 0:
            print(f"🔄 从checkpoint恢复训练: {args.resume}")
        checkpoint = torch.load(args.resume, map_location=args.device)

        # 加载模型状态
        if args.multi_gpu:
            model.module.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint['model_state_dict'])

        # 加载优化器和调度器状态
        adam_optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

        # 恢复训练状态
        epoch_start_idx = checkpoint['epoch'] + 1
        best_perform = checkpoint.get('best_perform', 0)
        early_stop = checkpoint.get('early_stop', 0)

        if rank == 0:
            print(f"✅ 恢复成功！从第 {epoch_start_idx} 个epoch开始，最佳性能: {best_perform:.4f}")
    elif hasattr(args, 'resume') and args.resume:
        if rank == 0:
            print(f"⚠️ 警告：checkpoint文件不存在: {args.resume}")
            print("🔄 将从头开始训练...")
    
    eval_set_use = eval_set[1]
    if len(eval_set_use)>10000:
        users = random.sample(list(eval_set_use), 10000)
    else:
        users = list(eval_set_use)
    
    user_list = []
    for u in users:
        if len(user_test[u]) < 1: continue
        user_list.append(u)

    inference_data_set = SeqDataset_Inference(user_train, user_valid, user_test, user_list, itemnum, args.maxlen)
    if args.multi_gpu:
        if rank == 0:
            inference_data_loader = DataLoader(
                inference_data_set,
                batch_size=args.batch_size_infer,
                sampler=DistributedSampler(inference_data_set, shuffle=True),
                pin_memory=True,
                num_workers=4,
            )
        else:
            inference_data_loader = None
    else:
        inference_data_loader = DataLoader(
            inference_data_set,
            batch_size=args.batch_size_infer,
            pin_memory=True,
            num_workers=4,
        )
        
    for epoch in tqdm(range(epoch_start_idx, args.num_epochs + 1)):
        model.train()
        if args.multi_gpu:
            train_data_loader.sampler.set_epoch(epoch)

        epoch_loss = 0.0
        step_count = 0

        # 完整训练一个epoch的所有数据
        for step, data in enumerate(train_data_loader):
            u, seq, pos, neg = data
            u, seq, pos, neg = u.numpy(), seq.numpy(), pos.numpy(), neg.numpy()

            # 前向传播和反向传播
            loss = model([u,seq,pos,neg], optimizer=adam_optimizer, batch_iter=[epoch,args.num_epochs + 1,step,num_batch], mode='phase2')

            if isinstance(loss, torch.Tensor):
                epoch_loss += loss.item()
            step_count += 1

            # 中间进度显示（不中断训练，仅记录进度）
            if step % (num_batch//5) == 0 and step != 0 and rank == 0:
                current_lr = adam_optimizer.param_groups[0]['lr']
                avg_loss = epoch_loss / step_count if step_count > 0 else 0
                print(f"  Epoch {epoch} Step {step}/{num_batch}, Loss: {avg_loss:.4f}, LR: {current_lr:.6f}")

        # Epoch结束后的完整验证（仅在rank==0执行）
        if rank == 0:
            eval_set_use = eval_set[0]
            if len(eval_set_use) > 10000:
                users = random.sample(list(eval_set_use), 10000)
            else:
                users = list(eval_set_use)

            user_list_valid = []
            for u in users:
                if len(user_valid[u]) < 1:
                    continue
                user_list_valid.append(u)

            valid_data_set = SeqDataset_Validation(user_train, user_valid, user_list_valid, itemnum, args.maxlen)
            valid_data_loader = DataLoader(
                valid_data_set,
                batch_size=args.batch_size_infer,
                pin_memory=True,
                shuffle=True,
                num_workers=4,
            )

            model.eval()
            core_model = model.module if args.multi_gpu else model
            core_model.users = 0.0
            core_model.NDCG = 0.0
            core_model.HT = 0.0
            core_model.NDCG_20 = 0.0
            core_model.HIT_20 = 0.0
            core_model.all_embs = None

            with torch.no_grad():
                for _, data in enumerate(valid_data_loader):
                    if _ == 0:
                        print(f"Validation (epoch {epoch}), early stop: {early_stop}")
                    u, seq, pos, neg = data
                    u, seq, pos, neg = u.numpy(), seq.numpy(), pos.numpy(), neg.numpy()
                    model([u, seq, pos, neg, rank, None, 'original'], mode='generate_batch')

            perform = core_model.HT / core_model.users if core_model.users > 0 else 0
            print(f"Epoch {epoch}: HR = {perform:.4f}, Best = {best_perform:.4f}")

            if perform >= best_perform:
                best_perform = perform
                if args.multi_gpu:
                    model.module.save_model(args, epoch2=epoch, best=True)
                else:
                    model.save_model(args, epoch2=epoch, best=True)

                # 测试最佳模型
                core_model.users = 0.0
                core_model.NDCG = 0.0
                core_model.HT = 0.0
                core_model.NDCG_20 = 0.0
                core_model.HIT_20 = 0.0
                with torch.no_grad():
                    for _, data in enumerate(inference_data_loader):
                        if _ == 0:
                            print(f"Testing ({len(inference_data_loader)} batches)")
                        u, seq, pos, neg = data
                        u, seq, pos, neg = u.numpy(), seq.numpy(), pos.numpy(), neg.numpy()
                        model([u, seq, pos, neg, rank, None, 'original'], mode='generate_batch')

                out_dir = f'./models/{args.save_dir}/'
                out_dir = out_dir[:-1] + 'best/'
                out_dir += f'{args.rec_pre_trained_data}_'
                out_dir += f'{args.llm}_{epoch}_results.txt'

                den = core_model.users if core_model.users and core_model.users > 0 else 1.0
                with open(out_dir, 'a') as f:
                    f.write(f'NDCG: {core_model.NDCG/den}, HR: {core_model.HT/den}\n')
                    f.write(f'NDCG20: {core_model.NDCG_20/den}, HR20: {core_model.HIT_20/den}\n')

                early_stop = 0
                print(f"🎉 新的最佳性能! HR: {core_model.HT/den:.4f}")
            else:
                if args.multi_gpu:
                    model.module.save_model(args, epoch2=epoch)
                else:
                    model.save_model(args, epoch2=epoch)
                early_stop += 1
                print(f"⚠️ 性能未提升，早停计数: {early_stop}/{early_thres}")

                # 保存checkpoint（每个epoch或按指定间隔）
                if rank == 0 and hasattr(args, 'save_checkpoint_every') and (epoch % args.save_checkpoint_every == 0 or early_stop == early_thres):
                    checkpoint_dir = f'./checkpoints/{args.save_dir}'
                    os.makedirs(checkpoint_dir, exist_ok=True)
                    checkpoint_path = f'{checkpoint_dir}/checkpoint_epoch_{epoch}.pt'

                    checkpoint = {
                        'epoch': epoch,
                        'model_state_dict': model.module.state_dict() if args.multi_gpu else model.state_dict(),
                        'optimizer_state_dict': adam_optimizer.state_dict(),
                        'scheduler_state_dict': scheduler.state_dict(),
                        'best_perform': best_perform,
                        'early_stop': early_stop,
                        'args': args
                    }
                    torch.save(checkpoint, checkpoint_path)
                    print(f"💾 Checkpoint保存到: {checkpoint_path}")

                    # 保留最新的checkpoint链接
                    latest_path = f'{checkpoint_dir}/checkpoint_latest.pt'
                    if os.path.exists(latest_path):
                        os.remove(latest_path)
                    # Windows系统使用copy而不是symlink
                    import shutil
                    shutil.copy2(checkpoint_path, latest_path)

                if early_stop == early_thres:
                    print("🛑 达到早停条件，训练结束")
                    sys.exit("Terminating Train")

            # 保存checkpoint（每个epoch或按指定间隔）
            if hasattr(args, 'save_checkpoint_every') and (epoch % args.save_checkpoint_every == 0):
                checkpoint_dir = f'./checkpoints/{args.save_dir}'
                os.makedirs(checkpoint_dir, exist_ok=True)
                checkpoint_path = f'{checkpoint_dir}/checkpoint_epoch_{epoch}.pt'

                checkpoint = {
                    'epoch': epoch,
                    'model_state_dict': model.module.state_dict() if args.multi_gpu else model.state_dict(),
                    'optimizer_state_dict': adam_optimizer.state_dict(),
                    'scheduler_state_dict': scheduler.state_dict(),
                    'best_perform': best_perform,
                    'early_stop': early_stop,
                    'args': args
                }
                torch.save(checkpoint, checkpoint_path)
                print(f"💾 Checkpoint保存到: {checkpoint_path}")

                # 保留最新的checkpoint链接
                latest_path = f'{checkpoint_dir}/checkpoint_latest.pt'
                if os.path.exists(latest_path):
                    os.remove(latest_path)
                # Windows系统使用copy而不是symlink
                import shutil
                shutil.copy2(checkpoint_path, latest_path)

        # 在epoch结束时更新学习率调度器（所有GPU都执行）
        scheduler.step()
        model.train()

    
    print('train time :', time.time() - t0)
    if args.multi_gpu:
        destroy_process_group()
    return


def extract_model(args):
    """
    提取用户和物品表示向量
    """
    print(f"开始提取表示向量...")
    print(f"数据集: {args.rec_pre_trained_data}")
    print(f"模型: {args.llm}")
    print(f"保存目录: {args.save_dir}")

    # 设置设备
    device = args.device

    # 加载数据
    dataset = data_partition(args.rec_pre_trained_data, args, path=f'./SeqRec/data_{args.rec_pre_trained_data}/{args.rec_pre_trained_data}')
    [user_train, user_valid, user_test, usernum, itemnum, eval_set] = dataset
    print('用户数:', usernum, '物品数:', itemnum)

    # 创建模型
    model = llmrec_model(args)
    model = model.to(device)

    # 加载训练好的模型权重
    try:
        # 从保存目录中查找模型文件来确定epoch
        import glob
        model_files = glob.glob(f'./models/{args.save_dir}/{args.rec_pre_trained_data}_{args.llm}_*_item_proj.pt')
        if model_files:
            # 从文件名中提取epoch数字
            # 文件名格式: Movies_and_TV_llama-3b_1_item_proj.pt
            filename = os.path.basename(model_files[0])
            # 移除.pt后缀，然后按_分割
            name_parts = filename.replace('.pt', '').split('_')
            # 找到数字部分（应该在llama-3b之后）
            for i, part in enumerate(name_parts):
                if part.isdigit():
                    phase2_epoch = int(part)
                    break
            else:
                phase2_epoch = 1  # 如果没找到数字，使用默认值
            print(f"找到模型文件: {filename}, epoch: {phase2_epoch}")
        else:
            print("未找到模型文件，使用默认epoch=1")
            phase2_epoch = 1

        model.load_model(args, phase2_epoch=phase2_epoch)
        print("✅ 模型权重加载成功")
    except Exception as e:
        print(f"❌ 模型权重加载失败: {e}")
        return

    model.eval()

    # 准备提取数据 - 使用最简单的SeqDataset，只需要训练数据
    user_list_extract = list(user_train.keys())
    # 使用SeqDataset，它只需要user_train数据，不依赖验证或测试数据
    extract_data_set = SeqDataset(user_train, len(user_list_extract), itemnum, args.maxlen)
    extract_data_loader = DataLoader(
        extract_data_set,
        batch_size=args.batch_size_infer,
        pin_memory=True,
        shuffle=False,
        num_workers=0  # 禁用多进程以避免索引问题
    )

    print(f"开始提取 {len(user_list_extract)} 个用户的表示向量...")

    # 提取用户表示
    with torch.no_grad():
        for step, data in enumerate(tqdm(extract_data_loader, desc="提取用户表示")):
            # SeqDataset返回(user_id, seq, pos, neg)，需要扩展为extract_emb期望的格式
            u, seq, pos, neg = data
            # 扩展数据格式以匹配extract_emb的期望：(u, seq, pos, neg, original_seq, rank, files)
            extended_data = (u, seq, pos, neg, seq, None, None)  # original_seq=seq, rank=None, files=None
            model(extended_data, mode='extract')

    # 保存提取的表示
    if hasattr(model, 'extract_embs_list') and model.extract_embs_list:
        import pickle
        extract_embs = torch.cat(model.extract_embs_list, dim=0)

        # 创建保存目录
        save_path = f'./models/{args.save_dir}/'
        create_dir(save_path)

        # 保存用户表示
        user_emb_path = f'{save_path}{args.rec_pre_trained_data}_{args.llm}_user_embeddings.pkl'
        with open(user_emb_path, 'wb') as f:
            pickle.dump(extract_embs.numpy(), f)

        print(f"✅ 用户表示已保存到: {user_emb_path}")
        print(f"表示形状: {extract_embs.shape}")
    else:
        print("❌ 没有提取到用户表示")

    print("🎉 表示提取完成！")
