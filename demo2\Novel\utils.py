"""
工具函数

包含配置管理、结果跟踪、数据处理等工具函数。
"""

import json
import yaml
import os
import time
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器"""
    
    @staticmethod
    def load_config(config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        config_path = Path(config_path)
        
        if not config_path.exists():
            raise FileNotFoundError(f"Config file not found: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            if config_path.suffix.lower() in ['.yaml', '.yml']:
                config = yaml.safe_load(f)
            elif config_path.suffix.lower() == '.json':
                config = json.load(f)
            else:
                raise ValueError(f"Unsupported config format: {config_path.suffix}")
        
        return config
    
    @staticmethod
    def save_config(config: Dict[str, Any], save_path: str):
        """保存配置文件"""
        save_path = Path(save_path)
        save_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(save_path, 'w', encoding='utf-8') as f:
            if save_path.suffix.lower() in ['.yaml', '.yml']:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            elif save_path.suffix.lower() == '.json':
                json.dump(config, f, indent=2, ensure_ascii=False)
            else:
                raise ValueError(f"Unsupported config format: {save_path.suffix}")
    
    @staticmethod
    def get_default_config() -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'device': 'cpu',
            'inference_mode': 'collaborative',
            'edge_model': {
                'item_num': 10000,
                'hidden_size': 64,
                'max_seq_length': 50,
                'num_attention_heads': 2,
                'num_hidden_layers': 2,
                'dropout_prob': 0.1,
                'user_repr_dim': 64
            },
            'cloud_model': {
                'user_repr_dim': 64,
                'item_num': 10000,
                'hidden_size': 256
            },
            'privacy': {
                'user_repr_dim': 64,
                'privacy_level': 'high',
                'enable_differential_privacy': True,
                'noise_scale': 0.1
            },
            'distillation': {
                'distillation_temperature': 4.0,
                'distillation_alpha': 0.7,
                'distillation_beta': 0.3
            },
            'training': {
                'batch_size': 32,
                'learning_rate': 0.001,
                'num_epochs': 10,
                'weight_decay': 0.01
            }
        }


class ResultTracker:
    """结果跟踪器"""
    
    def __init__(self, experiment_name: str, save_dir: str = "experiments"):
        self.experiment_name = experiment_name
        self.save_dir = Path(save_dir) / "runs" / experiment_name
        self.save_dir.mkdir(parents=True, exist_ok=True)
        
        self.results = {
            'experiment_name': experiment_name,
            'start_time': time.time(),
            'config': {},
            'training_history': [],
            'evaluation_results': {},
            'performance_stats': {},
            'privacy_stats': {}
        }
        
        # 设置日志
        self._setup_logging()
    
    def _setup_logging(self):
        """设置实验日志"""
        log_file = self.save_dir / "experiment.log"
        
        # 创建logger
        self.logger = logging.getLogger(f"experiment_{self.experiment_name}")
        self.logger.setLevel(logging.INFO)
        
        # 文件处理器
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        if not self.logger.handlers:
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
    
    def log_config(self, config: Dict[str, Any]):
        """记录配置"""
        self.results['config'] = config
        self.logger.info(f"Experiment config: {json.dumps(config, indent=2)}")
    
    def log_training_step(self, epoch: int, step: int, metrics: Dict[str, float]):
        """记录训练步骤"""
        training_record = {
            'epoch': epoch,
            'step': step,
            'timestamp': time.time(),
            **metrics
        }
        
        self.results['training_history'].append(training_record)
        
        # 记录到日志
        metrics_str = ", ".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
        self.logger.info(f"Epoch {epoch}, Step {step} - {metrics_str}")
    
    def log_evaluation(self, metrics: Dict[str, float]):
        """记录评估结果"""
        self.results['evaluation_results'] = metrics
        
        # 记录到日志
        metrics_str = ", ".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
        self.logger.info(f"Evaluation results - {metrics_str}")
    
    def log_performance_stats(self, stats: Dict[str, Any]):
        """记录性能统计"""
        self.results['performance_stats'] = stats
        self.logger.info(f"Performance stats: {json.dumps(stats, indent=2)}")
    
    def log_privacy_stats(self, stats: Dict[str, Any]):
        """记录隐私统计"""
        self.results['privacy_stats'] = stats
        self.logger.info(f"Privacy stats: {json.dumps(stats, indent=2)}")
    
    def save_results(self):
        """保存实验结果"""
        self.results['end_time'] = time.time()
        self.results['duration'] = self.results['end_time'] - self.results['start_time']
        
        # 保存到JSON文件
        results_file = self.save_dir / "results" / "latest_results.json"
        results_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Results saved to {results_file}")
        
        return results_file
    
    def get_results(self) -> Dict[str, Any]:
        """获取当前结果"""
        return self.results.copy()


class DataProcessor:
    """数据处理工具"""
    
    @staticmethod
    def pad_sequences(sequences: List[List[int]], max_length: int, padding_value: int = 0) -> List[List[int]]:
        """填充序列到指定长度"""
        padded_sequences = []
        for seq in sequences:
            if len(seq) >= max_length:
                padded_seq = seq[-max_length:]  # 截取最后max_length个
            else:
                padded_seq = seq + [padding_value] * (max_length - len(seq))
            padded_sequences.append(padded_seq)
        return padded_sequences
    
    @staticmethod
    def create_attention_mask(sequences: List[List[int]], padding_value: int = 0) -> List[List[int]]:
        """创建注意力掩码"""
        attention_masks = []
        for seq in sequences:
            mask = [1 if token != padding_value else 0 for token in seq]
            attention_masks.append(mask)
        return attention_masks
    
    @staticmethod
    def split_sequence(sequence: List[int], train_ratio: float = 0.8) -> tuple:
        """分割序列为训练和测试部分"""
        split_point = int(len(sequence) * train_ratio)
        train_seq = sequence[:split_point]
        test_seq = sequence[split_point:]
        return train_seq, test_seq


class MetricsCalculator:
    """评估指标计算器"""
    
    @staticmethod
    def calculate_ndcg(predictions: List[List[int]], ground_truth: List[List[int]], k: int = 10) -> float:
        """计算NDCG@K"""
        ndcg_scores = []
        
        for pred, truth in zip(predictions, ground_truth):
            # 简化的NDCG计算
            pred_k = pred[:k]
            truth_set = set(truth)
            
            dcg = 0.0
            for i, item in enumerate(pred_k):
                if item in truth_set:
                    dcg += 1.0 / np.log2(i + 2)
            
            # 理想DCG
            idcg = sum(1.0 / np.log2(i + 2) for i in range(min(len(truth), k)))
            
            ndcg = dcg / idcg if idcg > 0 else 0.0
            ndcg_scores.append(ndcg)
        
        return np.mean(ndcg_scores)
    
    @staticmethod
    def calculate_hit_rate(predictions: List[List[int]], ground_truth: List[List[int]], k: int = 10) -> float:
        """计算Hit Rate@K"""
        hits = 0
        total = len(predictions)
        
        for pred, truth in zip(predictions, ground_truth):
            pred_k = set(pred[:k])
            truth_set = set(truth)
            
            if pred_k & truth_set:  # 有交集
                hits += 1
        
        return hits / total if total > 0 else 0.0
    
    @staticmethod
    def calculate_recall(predictions: List[List[int]], ground_truth: List[List[int]], k: int = 10) -> float:
        """计算Recall@K"""
        recall_scores = []
        
        for pred, truth in zip(predictions, ground_truth):
            pred_k = set(pred[:k])
            truth_set = set(truth)
            
            intersection = pred_k & truth_set
            recall = len(intersection) / len(truth_set) if len(truth_set) > 0 else 0.0
            recall_scores.append(recall)
        
        return np.mean(recall_scores)


def setup_logging(level: str = "INFO"):
    """设置全局日志"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('novel_recommendation.log')
        ]
    )


def get_device() -> str:
    """获取可用设备"""
    if torch.cuda.is_available():
        return 'cuda'
    else:
        return 'cpu'


# 导入numpy用于计算
import numpy as np


class GPUManager:
    """GPU环境管理器"""

    @staticmethod
    def check_gpu_environment():
        """检查GPU环境"""
        try:
            import torch
        except ImportError:
            logger.error("PyTorch未安装")
            return False

        cuda_available = torch.cuda.is_available()
        if not cuda_available:
            logger.warning("CUDA不可用")
            return False

        gpu_count = torch.cuda.device_count()
        logger.info(f"检测到 {gpu_count} 个GPU")

        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            logger.info(f"GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")

        return gpu_count >= 1

    @staticmethod
    def test_multi_gpu():
        """测试多GPU功能"""
        try:
            import torch
            import torch.nn as nn
        except ImportError:
            logger.error("PyTorch未安装")
            return False

        if not torch.cuda.is_available():
            logger.warning("CUDA不可用")
            return False

        gpu_count = torch.cuda.device_count()
        if gpu_count < 2:
            logger.info("只有一个GPU，无法测试多GPU功能")
            return True

        try:
            # 创建简单模型测试
            model = nn.Linear(100, 10)
            model = nn.DataParallel(model)
            model = model.cuda()

            # 测试前向传播
            x = torch.randn(32, 100).cuda()
            y = model(x)

            logger.info(f"多GPU测试成功，使用 {gpu_count} 个GPU")
            return True

        except Exception as e:
            logger.error(f"多GPU测试失败: {e}")
            return False

    @staticmethod
    def get_gpu_memory_info():
        """获取GPU内存信息"""
        try:
            import torch
        except ImportError:
            return {}

        if not torch.cuda.is_available():
            return {}

        memory_info = {}
        for i in range(torch.cuda.device_count()):
            allocated = torch.cuda.memory_allocated(i) / 1024**3
            reserved = torch.cuda.memory_reserved(i) / 1024**3
            total = torch.cuda.get_device_properties(i).total_memory / 1024**3

            memory_info[f'gpu_{i}'] = {
                'allocated_gb': allocated,
                'reserved_gb': reserved,
                'total_gb': total,
                'available_gb': total - reserved
            }

        return memory_info

    @staticmethod
    def recommend_multi_gpu_config(gpu_count: int = None):
        """推荐多GPU配置"""
        try:
            import torch
        except ImportError:
            return {}

        if gpu_count is None:
            gpu_count = torch.cuda.device_count() if torch.cuda.is_available() else 0

        if gpu_count >= 2:
            return {
                'cf_srec_training': {
                    'batch_size': 512,
                    'hidden_units': 128,
                    'num_blocks': 4,
                    'maxlen': 128,
                    'fp16': True
                },
                'llm_training': {
                    'batch_size': 40,
                    'gradient_accumulation_steps': 4,
                    'fp16': True
                },
                'collaborative_training': {
                    'batch_size': 32,
                    'gradient_accumulation_steps': 2,
                    'fp16': True
                }
            }
        else:
            return {
                'cf_srec_training': {
                    'batch_size': 128,
                    'hidden_units': 64,
                    'num_blocks': 2,
                    'maxlen': 50,
                    'fp16': False
                },
                'llm_training': {
                    'batch_size': 20,
                    'gradient_accumulation_steps': 1,
                    'fp16': False
                },
                'collaborative_training': {
                    'batch_size': 16,
                    'gradient_accumulation_steps': 1,
                    'fp16': False
                }
            }
