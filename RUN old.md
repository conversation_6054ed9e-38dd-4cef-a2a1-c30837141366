# 端云协同推荐系统运行指南

## 📋 概述

本指南提供端云协同推荐系统在双GPU Linux服务器上的完整运行流程。系统基于原始LLM-SRec项目，通过适配器模式扩展了端云协同和隐私保护功能。

## 🎯 系统架构

```
原始LLM-SRec架构:
SASRec → RecSys封装 → LLM-SRec主模型

端云协同架构:
SASRec → RecSys封装 → EdgeCFSRecAdapter (+ 隐私保护)
                           ↓
                      安全传输通道
                           ↓
LLM4Rec → LLM逻辑 → CloudLLMEnhanced (+ 用户表示融合)
```

## 🔧 环境准备

### 1. 创建Python环境

```bash
# 创建conda环境
conda create -n edge_cloud_rec python=3.10
conda activate edge_cloud_rec
```

### 2. 安装依赖包

```bash
# 安装原始LLM-SRec依赖
pip install -r requirements.txt

# 安装端云协同系统额外依赖
pip install -r requirements_edge_cloud.txt
```

### 3. 验证双GPU环境

```bash
# 检查GPU状态
nvidia-smi

# 验证PyTorch GPU支持
python -c "import torch; print(f'CUDA可用: {torch.cuda.is_available()}')"
python -c "import torch; print(f'GPU数量: {torch.cuda.device_count()}')"

# 设置GPU可见性（使用两张GPU）
export CUDA_VISIBLE_DEVICES=0,1
```

## 📊 数据准备

### 1. 检查数据集

```bash
# 检查Movies_and_TV数据是否存在
ls -la SeqRec/data_Movies_and_TV/
```

### 2. 手动预处理（如需要）

```bash
# 如果数据不存在，手动预处理
cd SeqRec/sasrec
python data_preprocess.py --dataset Movies_and_TV
cd ../..
```

## 🚀 阶段一：SASRec预训练

### 1. 训练SASRec基础模型

```bash
# 进入SASRec目录
cd SeqRec/sasrec

# 训练SASRec模型（单GPU）
python main.py \
    --dataset Movies_and_TV \
    --device 0 \
    --batch_size 128 \
    --lr 0.001 \
    --maxlen 128 \
    --hidden_units 64 \
    --num_blocks 2 \
    --num_epochs 200 \
    --num_heads 1 \
    --dropout_rate 0.1

# 返回项目根目录
cd ../..
```

### 2. 验证SASRec模型

```bash
# 检查模型文件是否生成
ls -la SeqRec/sasrec/Movies_and_TV/
```

## 🤖 阶段二：LLM-SRec训练

### 1. 单GPU训练LLM-SRec

```bash
# 单GPU训练（推荐配置）
python main.py \
    --train \
    --device 0 \
    --llm llama-3b \
    --recsys sasrec \
    --rec_pre_trained_data Movies_and_TV \
    --save_dir model_train_single \
    --batch_size 20 \
    --num_epochs 10 \
    --maxlen 128 \
    --stage2_lr 0.0001
```

### 2. 双GPU训练LLM-SRec

```bash
# 双GPU并行训练
python main.py \
    --train \
    --multi_gpu \
    --world_size 2 \
    --llm llama-3b \
    --recsys sasrec \
    --rec_pre_trained_data Movies_and_TV \
    --save_dir model_train_multi \
    --batch_size 4 \
    --num_epochs 1 \
    --maxlen 128 \
    --stage2_lr 0.0001
```

### 3. 提取用户/物品表示

```bash
# 提取表示向量
python main.py \
    --extract \
    --device 0 \
    --llm llama-3b \
    --recsys sasrec \
    --rec_pre_trained_data Movies_and_TV \
    --save_dir model_train_multi
```

### 4. 验证训练结果

```bash
# 检查训练结果
ls -la model_train_multi/
```

## 🌐 阶段三：端云协同系统部署

### 1. 验证系统模块

```bash
# 测试端云协同系统导入
python -c "from client_cloud import EdgeCloudRecommendationSystem, SystemConfig; print('✅ 端云协同系统导入成功')"

# 测试隐私保护模块
python -c "from privacy import EdgeCloudPrivacyProtector, PrivacyConfig; print('✅ 隐私保护模块导入成功')"
```

### 2. 运行端侧模型测试

```bash
# 测试端侧模型初始化和推荐
python -c "
import torch
from client_cloud import create_edge_cf_srec

class Args:
    def __init__(self):
        self.device = torch.device('cuda:0')
        self.recsys = 'sasrec'
        self.rec_pre_trained_data = 'Movies_and_TV'

args = Args()
edge_model = create_edge_cf_srec(args, privacy_level='high')
user_sequence = [1, 5, 23, 45, 67, 89, 123, 156, 234, 345]
user_repr = edge_model.generate_user_representation(user_sequence, 'test_user')
print(f'✅ 端侧模型测试成功，用户表示形状: {user_repr.shape}')
"
```

### 3. 运行云端模型测试

```bash
# 测试云端模型初始化和推荐
python -c "
import torch
from client_cloud import create_cloud_llm_enhanced

class Args:
    def __init__(self):
        self.device = torch.device('cuda:0')
        self.llm = 'llama-3b'
        self.rec_pre_trained_data = 'Movies_and_TV'

args = Args()
cloud_model = create_cloud_llm_enhanced(args, privacy_level='high')
user_repr = torch.randn(64).cuda()
recommendations = cloud_model.generate_recommendations(user_repr, 'test_user', num_recommendations=10)
print(f'✅ 云端模型测试成功，推荐数量: {recommendations[\"num_recommendations\"]}')
"
```

## 🔄 阶段四：推理阶段 (Inference)

### 🎯 重要说明

**训练过程中已自动进行推理评估！**

训练时会在验证性能提升时自动进行测试集推理，并保存结果到：
```
./models/{save_dir}/best/{dataset}_{llm}_{epoch}_results.txt
```

### 🚀 运行命令

#### **方式一：训练中自动推理（推荐，LLM-SRec风格）**
```bash
# 单GPU训练（推荐）
python main.py \
    --train \
    --device 0 \
    --llm llama-3b \
    --recsys sasrec \
    --rec_pre_trained_data Movies_and_TV \
    --save_dir model_train \
    --batch_size 20 \
    --num_epochs 10

# 双GPU训练
python main.py \
    --train \
    --multi_gpu \
    --world_size 2 \
    --llm llama-3b \
    --recsys sasrec \
    --rec_pre_trained_data Movies_and_TV \
    --save_dir model_train_multi \
    --batch_size 10 \
    --num_epochs 5
```

#### **方式二：独立推理评估（可选）**
```bash
# 快速推理验证（100个用户）
python infer.py --num_users 100

# 大规模推理（1000个用户）
python infer.py --num_users 1000
```

### 📊 预期输出

#### **训练中的推理输出**
```
Validation (epoch 3), early stop: 0
Testing (15 batches)
NDCG: 0.1234, HR: 0.2345
NDCG20: 0.1456, HR20: 0.2567
```

#### **结果文件**
```
./models/model_train/best/Movies_and_TV_llama-3b_3_results.txt
```

## 📈 阶段五：结果分析

### 查看训练和推理结果

```bash
# 查看SASRec训练结果
cat SeqRec/data_Movies_and_TV/Results.txt

# 查看LLM-SRec推理结果
ls -la models/model_train/best/
cat models/model_train/best/Movies_and_TV_llama-3b_*_results.txt
```

## 🎯 快速启动命令

### 完整流程一键运行

```bash
# 设置GPU环境
export CUDA_VISIBLE_DEVICES=0,1

# 1. 训练SASRec
cd SeqRec/sasrec && python main.py --dataset Movies_and_TV --device 0 --num_epochs 200 && cd ../..

# 2. 训练LLM-SRec（单GPU推荐）
python main.py --train --device 0 --llm llama-3b --recsys sasrec --rec_pre_trained_data Movies_and_TV --save_dir model_train --batch_size 20 --num_epochs 10

# 3. 查看推理结果
cat models/model_train/best/Movies_and_TV_llama-3b_*_results.txt
```

## ❓ 故障排除

```bash
# 1. CUDA内存不足 - 减少batch_size
python main.py --train --device 0 --batch_size 10 --llm llama-3b --recsys sasrec

# 2. 模型文件缺失 - 重新训练SASRec
cd SeqRec/sasrec && python main.py --dataset Movies_and_TV --device 0 && cd ../..

# 3. 检查GPU状态
nvidia-smi
python -c "import torch; print(torch.cuda.is_available())"
```

## 🎉 部署完成

**端云协同推荐系统已成功部署！**

系统功能：
- 🔒 隐私保护的用户建模
- ⚡ 快速的端侧推荐
- ☁️ 深度的云端优化
- 🤝 智能的端云协同
- 📚 持续的知识蒸馏

**硬件配置：双GPU Linux服务器**
**数据集：Amazon Movies_and_TV**
**模型：SASRec + LLM-SRec + 端云协同适配器**
