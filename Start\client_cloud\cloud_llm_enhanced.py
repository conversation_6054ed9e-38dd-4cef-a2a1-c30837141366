"""
云端序列知识增强LLM模型

本模块实现云端的序列知识增强LLM模型，主要功能：
1. 接收端侧传输的64维用户表示向量
2. 结合LLM的文本理解能力进行深度推荐优化
3. 集成序列知识到LLM的推荐流程中
4. 提供高质量的个性化推荐服务

核心特性：
- 用户表示融合：将64维用户向量融入LLM
- 序列知识增强：保留序列建模的优势
- 深度推荐优化：利用LLM的强大能力
- 安全数据处理：处理加密的用户表示
"""

# 设置Hugging Face镜像
import os
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import json
import pickle
from transformers import AutoModelForCausalLM, AutoTokenizer
from transformers.generation import GenerationConfig

from models.seqllm4rec import llm4rec
from privacy import EdgeCloudPrivacyProtector, PrivacyConfig


class CloudLLMEnhanced(nn.Module):
    """
    云端序列知识增强LLM模型
    
    基于现有LLM4Rec架构，增强对端侧用户表示的处理能力：
    1. 接收并解密端侧用户表示
    2. 将用户表示融入LLM的推荐流程
    3. 生成高质量的个性化推荐
    4. 支持批量推理和实时响应
    """
    
    def __init__(self, args, privacy_config: Optional[PrivacyConfig] = None):
        """
        初始化云端LLM增强模型
        
        Args:
            args: 模型配置参数
            privacy_config: 隐私保护配置
        """
        super().__init__()
        self.args = args
        self.device = args.device
        
        # 初始化基础LLM4Rec模型
        self.llm = llm4rec(device=self.device, llm_model=args.llm, args=args)
        
        # 加载物品文本信息
        with open(f'./SeqRec/data_{args.rec_pre_trained_data}/text_name_dict.json.gz', 'rb') as ft:
            self.text_name_dict = pickle.load(ft)
        
        # 初始化隐私保护组件（用于解密）
        if privacy_config is None:
            privacy_config = PrivacyConfig(user_repr_dim=64)
        self.privacy_protector = EdgeCloudPrivacyProtector(privacy_config)
        
        # 用户表示融合层
        self.user_repr_fusion = nn.Sequential(
            nn.Linear(64, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, self.llm.llm_model.config.hidden_size),
            nn.LayerNorm(self.llm.llm_model.config.hidden_size)
        ).to(self.device)  # 确保融合层在正确的设备上

        # 序列知识增强层
        self.sequence_knowledge_enhancer = nn.MultiheadAttention(
            embed_dim=self.llm.llm_model.config.hidden_size,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        ).to(self.device)  # 确保注意力层在正确的设备上
        
        # 推荐生成配置
        self.generation_config = GenerationConfig(
            max_new_tokens=100,
            temperature=0.7,
            top_p=0.9,
            do_sample=True,
            pad_token_id=self.llm.llm_tokenizer.eos_token_id
        )
        
        # 缓存和统计
        self.recommendation_cache = {}
        self.inference_stats = {
            'total_requests': 0,
            'cache_hits': 0,
            'average_response_time': 0.0
        }
        
        # 初始化新增层的参数
        self._initialize_new_layers()
    
    def _initialize_new_layers(self):
        """初始化新增层的参数"""
        for module in self.user_repr_fusion:
            if isinstance(module, nn.Linear):
                nn.init.xavier_normal_(module.weight)
                nn.init.zeros_(module.bias)
    
    def process_encrypted_user_representation(
        self, 
        encrypted_package: Dict[str, Any]
    ) -> torch.Tensor:
        """
        处理加密的用户表示
        
        Args:
            encrypted_package: 端侧发送的加密数据包
            
        Returns:
            解密后的用户表示向量
        """
        # 解密用户表示
        decrypted_data = self.privacy_protector.communicator.decrypt_user_representation(
            encrypted_package
        )
        
        # 转换为tensor
        user_repr = torch.tensor(
            decrypted_data['user_representation'], 
            dtype=torch.float32,
            device=self.device
        )
        
        return user_repr
    
    def fuse_user_representation(
        self, 
        user_repr: torch.Tensor,
        text_embeddings: torch.Tensor
    ) -> torch.Tensor:
        """
        融合用户表示到文本嵌入中
        
        Args:
            user_repr: 64维用户表示 [batch_size, 64] 或 [64]
            text_embeddings: 文本嵌入 [batch_size, seq_len, hidden_size]
            
        Returns:
            融合后的文本嵌入
        """
        # 确保用户表示有正确的维度
        if len(user_repr.shape) == 1:
            user_repr = user_repr.unsqueeze(0)  # [1, 64]
        
        # 将用户表示投影到LLM的隐藏维度
        user_repr_projected = self.user_repr_fusion(user_repr)  # [batch_size, hidden_size]
        
        # 扩展用户表示以匹配序列长度
        batch_size, seq_len, hidden_size = text_embeddings.shape
        user_repr_expanded = user_repr_projected.unsqueeze(1).expand(
            batch_size, seq_len, hidden_size
        )  # [batch_size, seq_len, hidden_size]
        
        # 使用注意力机制融合用户表示和文本嵌入
        enhanced_embeddings, _ = self.sequence_knowledge_enhancer(
            query=text_embeddings,
            key=user_repr_expanded,
            value=user_repr_expanded
        )
        
        # 残差连接
        fused_embeddings = text_embeddings + enhanced_embeddings
        
        return fused_embeddings
    
    def generate_recommendations(
        self, 
        user_repr: torch.Tensor,
        user_id: str,
        num_recommendations: int = 10,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        生成个性化推荐
        
        Args:
            user_repr: 用户表示向量
            user_id: 用户ID
            num_recommendations: 推荐数量
            use_cache: 是否使用缓存
            
        Returns:
            推荐结果字典
        """
        import time
        start_time = time.time()
        
        # 检查缓存
        cache_key = f"{user_id}_{num_recommendations}"
        if use_cache and cache_key in self.recommendation_cache:
            self.inference_stats['cache_hits'] += 1
            return self.recommendation_cache[cache_key]
        
        # 构建推荐提示
        prompt = self._build_recommendation_prompt(user_repr, num_recommendations)
        
        # 分词处理
        inputs = self.llm.llm_tokenizer(
            prompt,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=1024
        ).to(self.device)
        
        # 获取输入嵌入
        inputs_embeds = self.llm.llm_model.get_input_embeddings()(inputs['input_ids'])
        
        # 融合用户表示
        fused_embeddings = self.fuse_user_representation(user_repr, inputs_embeds)
        
        # 生成推荐
        with torch.no_grad():
            outputs = self.llm.llm_model.generate(
                inputs_embeds=fused_embeddings,
                attention_mask=inputs['attention_mask'],
                generation_config=self.generation_config,
                return_dict_in_generate=True,
                output_scores=True
            )
        
        # 解码结果
        generated_text = self.llm.llm_tokenizer.decode(
            outputs.sequences[0], 
            skip_special_tokens=True
        )
        
        # 解析推荐结果
        recommendations = self._parse_recommendations(generated_text)
        
        # 构建结果
        result = {
            'user_id': user_id,
            'recommendations': recommendations,
            'generated_text': generated_text,
            'num_recommendations': len(recommendations),
            'timestamp': time.time(),
            'response_time': time.time() - start_time
        }
        
        # 更新缓存和统计
        if use_cache:
            self.recommendation_cache[cache_key] = result
        
        self.inference_stats['total_requests'] += 1
        self.inference_stats['average_response_time'] = (
            (self.inference_stats['average_response_time'] * (self.inference_stats['total_requests'] - 1) + 
             result['response_time']) / self.inference_stats['total_requests']
        )
        
        return result
    
    def _build_recommendation_prompt(
        self, 
        user_repr: torch.Tensor, 
        num_recommendations: int
    ) -> str:
        """
        构建推荐提示
        
        Args:
            user_repr: 用户表示向量
            num_recommendations: 推荐数量
            
        Returns:
            推荐提示文本
        """
        # 基于用户表示的特征构建提示
        user_repr_norm = torch.norm(user_repr, p=2).item()
        user_repr_mean = torch.mean(user_repr).item()
        
        prompt = f"""Based on the user's preference profile (norm: {user_repr_norm:.2f}, mean: {user_repr_mean:.2f}), 
recommend {num_recommendations} items that the user might be interested in. 

Please provide recommendations in the following format:
1. Item Title - Brief description
2. Item Title - Brief description
...

Recommendations:"""
        
        return prompt
    
    def _parse_recommendations(self, generated_text: str) -> List[Dict[str, Any]]:
        """
        解析生成的推荐文本
        
        Args:
            generated_text: 生成的文本
            
        Returns:
            解析后的推荐列表
        """
        recommendations = []
        
        # 简单的文本解析逻辑
        lines = generated_text.split('\n')
        for line in lines:
            line = line.strip()
            if line and (line[0].isdigit() or line.startswith('-')):
                # 移除编号
                if '.' in line:
                    content = line.split('.', 1)[1].strip()
                elif line.startswith('-'):
                    content = line[1:].strip()
                else:
                    content = line
                
                # 分离标题和描述
                if ' - ' in content:
                    title, description = content.split(' - ', 1)
                else:
                    title = content
                    description = ""
                
                recommendations.append({
                    'title': title.strip(),
                    'description': description.strip(),
                    'confidence': 0.8  # 默认置信度
                })
        
        return recommendations
    
    def batch_generate_recommendations(
        self, 
        user_reprs: List[torch.Tensor],
        user_ids: List[str],
        num_recommendations: int = 10
    ) -> List[Dict[str, Any]]:
        """
        批量生成推荐
        
        Args:
            user_reprs: 用户表示向量列表
            user_ids: 用户ID列表
            num_recommendations: 推荐数量
            
        Returns:
            批量推荐结果
        """
        results = []
        
        for user_repr, user_id in zip(user_reprs, user_ids):
            result = self.generate_recommendations(
                user_repr, user_id, num_recommendations, use_cache=False
            )
            results.append(result)
        
        return results
    
    def get_inference_statistics(self) -> Dict[str, Any]:
        """
        获取推理统计信息
        
        Returns:
            推理统计信息
        """
        cache_hit_rate = (
            self.inference_stats['cache_hits'] / max(self.inference_stats['total_requests'], 1)
        )
        
        return {
            'total_requests': self.inference_stats['total_requests'],
            'cache_hits': self.inference_stats['cache_hits'],
            'cache_hit_rate': cache_hit_rate,
            'average_response_time': self.inference_stats['average_response_time'],
            'cache_size': len(self.recommendation_cache)
        }
    
    def clear_cache(self):
        """清空推荐缓存"""
        self.recommendation_cache.clear()
        self.inference_stats['cache_hits'] = 0
    
    def save_model(self, save_path: str):
        """
        保存模型
        
        Args:
            save_path: 保存路径
        """
        save_dict = {
            'user_repr_fusion': self.user_repr_fusion.state_dict(),
            'sequence_knowledge_enhancer': self.sequence_knowledge_enhancer.state_dict(),
            'args': self.args,
            'privacy_config': self.privacy_protector.config
        }
        
        torch.save(save_dict, save_path)
    
    def load_model(self, load_path: str):
        """
        加载模型
        
        Args:
            load_path: 加载路径
        """
        checkpoint = torch.load(load_path, map_location=self.device)
        
        self.user_repr_fusion.load_state_dict(checkpoint['user_repr_fusion'])
        self.sequence_knowledge_enhancer.load_state_dict(checkpoint['sequence_knowledge_enhancer'])
        
        if 'privacy_config' in checkpoint:
            self.privacy_protector = EdgeCloudPrivacyProtector(checkpoint['privacy_config'])


def create_cloud_llm_enhanced(args, privacy_level: str = 'high') -> CloudLLMEnhanced:
    """
    创建云端LLM增强模型的便捷函数
    
    Args:
        args: 模型配置参数
        privacy_level: 隐私保护级别
        
    Returns:
        CloudLLMEnhanced模型实例
    """
    privacy_config = PrivacyConfig(
        user_repr_dim=64,
        privacy_level=privacy_level,
        enable_encryption=True
    )
    
    return CloudLLMEnhanced(args, privacy_config)
