"""
隐私保护的用户表示传输机制

本模块实现端云协同推荐系统中的隐私保护机制，确保：
1. 用户原始交互数据完全保留在端侧设备
2. 仅传输64维用户表示向量到云端
3. 通过差分隐私保护算法防止从表示向量反推原始数据
4. 提供安全的端云通信协议

核心功能：
- 差分隐私保护：为用户表示添加校准噪声
- 表示向量压缩：确保传输效率
- 安全通信：加密传输和身份验证
- 隐私风险评估：动态评估和调整隐私保护级别
"""

import torch
import torch.nn as nn
import numpy as np
import hashlib
import hmac
import json
import time
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os


@dataclass
class PrivacyConfig:
    """隐私保护配置"""
    # 差分隐私参数
    epsilon: float = 1.0  # 隐私预算
    delta: float = 1e-5   # 失败概率
    sensitivity: float = 1.0  # 敏感度
    
    # 表示向量参数
    user_repr_dim: int = 64  # 用户表示维度
    max_norm: float = 10.0   # 最大L2范数约束
    
    # 通信安全参数
    enable_encryption: bool = True
    key_rotation_interval: int = 3600  # 密钥轮换间隔(秒)
    
    # 隐私保护级别
    privacy_level: str = 'high'  # 'low', 'medium', 'high'


class DifferentialPrivacyProtector:
    """
    差分隐私保护器
    
    实现拉普拉斯机制和高斯机制，为用户表示添加校准噪声，
    确保满足(ε,δ)-差分隐私要求
    """
    
    def __init__(self, config: PrivacyConfig):
        self.config = config
        self.noise_scale = self._calculate_noise_scale()
        
    def _calculate_noise_scale(self) -> float:
        """计算噪声尺度"""
        if self.config.privacy_level == 'high':
            # 高隐私级别：使用更大的噪声
            return self.config.sensitivity / (self.config.epsilon * 0.5)
        elif self.config.privacy_level == 'medium':
            # 中等隐私级别
            return self.config.sensitivity / self.config.epsilon
        else:
            # 低隐私级别：使用较小的噪声
            return self.config.sensitivity / (self.config.epsilon * 2.0)
    
    def add_laplace_noise(self, user_repr: torch.Tensor) -> torch.Tensor:
        """
        添加拉普拉斯噪声
        
        Args:
            user_repr: 原始用户表示 [batch_size, repr_dim] 或 [repr_dim]
            
        Returns:
            添加噪声后的用户表示
        """
        # 生成拉普拉斯噪声
        noise = torch.from_numpy(
            np.random.laplace(0, self.noise_scale, user_repr.shape)
        ).float()
        
        # 添加噪声
        noisy_repr = user_repr + noise
        
        # 应用范数约束
        return self._apply_norm_constraint(noisy_repr)
    
    def add_gaussian_noise(self, user_repr: torch.Tensor) -> torch.Tensor:
        """
        添加高斯噪声（用于(ε,δ)-差分隐私）
        
        Args:
            user_repr: 原始用户表示
            
        Returns:
            添加噪声后的用户表示
        """
        # 计算高斯噪声的标准差
        sigma = np.sqrt(2 * np.log(1.25 / self.config.delta)) * self.config.sensitivity / self.config.epsilon
        
        # 生成高斯噪声
        noise = torch.randn_like(user_repr) * sigma
        
        # 添加噪声
        noisy_repr = user_repr + noise
        
        # 应用范数约束
        return self._apply_norm_constraint(noisy_repr)
    
    def _apply_norm_constraint(self, repr_tensor: torch.Tensor) -> torch.Tensor:
        """应用L2范数约束"""
        if len(repr_tensor.shape) == 1:
            # 单个向量
            norm = torch.norm(repr_tensor, p=2)
            if norm > self.config.max_norm:
                repr_tensor = repr_tensor * (self.config.max_norm / norm)
        else:
            # 批量向量
            norms = torch.norm(repr_tensor, p=2, dim=-1, keepdim=True)
            mask = norms > self.config.max_norm
            repr_tensor = torch.where(
                mask,
                repr_tensor * (self.config.max_norm / norms),
                repr_tensor
            )
        
        return repr_tensor


class SecureCommunicator:
    """
    安全通信器
    
    提供端云之间的加密通信，包括：
    - 对称加密（AES）
    - 消息认证码（HMAC）
    - 密钥管理和轮换
    """
    
    def __init__(self, config: PrivacyConfig):
        self.config = config
        self.cipher_suite = None
        self.hmac_key = None
        self.last_key_rotation = 0
        
        if config.enable_encryption:
            self._initialize_encryption()
    
    def _initialize_encryption(self):
        """初始化加密组件"""
        # 生成密钥
        password = b"edge_cloud_recommendation_system"
        salt = os.urandom(16)
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        
        # 初始化加密套件
        self.cipher_suite = Fernet(key)
        self.hmac_key = os.urandom(32)
        self.last_key_rotation = time.time()
    
    def encrypt_user_representation(self, user_repr: torch.Tensor, user_id: str) -> Dict[str, Any]:
        """
        加密用户表示
        
        Args:
            user_repr: 用户表示向量
            user_id: 用户ID
            
        Returns:
            加密后的数据包
        """
        if not self.config.enable_encryption:
            return {
                'user_id': user_id,
                'user_representation': user_repr.detach().cpu().numpy().tolist(),
                'encrypted': False,
                'timestamp': time.time()
            }
        
        # 检查是否需要密钥轮换
        self._check_key_rotation()
        
        # 准备数据
        data = {
            'user_id': user_id,
            'user_representation': user_repr.detach().cpu().numpy().tolist(),
            'timestamp': time.time()
        }
        
        # 序列化数据
        data_bytes = json.dumps(data).encode('utf-8')
        
        # 加密数据
        encrypted_data = self.cipher_suite.encrypt(data_bytes)
        
        # 生成HMAC
        hmac_signature = hmac.new(
            self.hmac_key,
            encrypted_data,
            hashlib.sha256
        ).hexdigest()
        
        return {
            'encrypted_data': base64.b64encode(encrypted_data).decode('utf-8'),
            'hmac_signature': hmac_signature,
            'encrypted': True,
            'timestamp': time.time()
        }
    
    def decrypt_user_representation(self, encrypted_package: Dict[str, Any]) -> Dict[str, Any]:
        """
        解密用户表示
        
        Args:
            encrypted_package: 加密数据包
            
        Returns:
            解密后的数据
        """
        if not encrypted_package.get('encrypted', False):
            return encrypted_package
        
        # 验证HMAC
        encrypted_data = base64.b64decode(encrypted_package['encrypted_data'])
        expected_hmac = hmac.new(
            self.hmac_key,
            encrypted_data,
            hashlib.sha256
        ).hexdigest()
        
        if not hmac.compare_digest(expected_hmac, encrypted_package['hmac_signature']):
            raise ValueError("HMAC验证失败，数据可能被篡改")
        
        # 解密数据
        decrypted_bytes = self.cipher_suite.decrypt(encrypted_data)
        data = json.loads(decrypted_bytes.decode('utf-8'))
        
        return data
    
    def _check_key_rotation(self):
        """检查并执行密钥轮换"""
        current_time = time.time()
        if current_time - self.last_key_rotation > self.config.key_rotation_interval:
            self._rotate_keys()
            self.last_key_rotation = current_time
    
    def _rotate_keys(self):
        """轮换加密密钥"""
        # 重新初始化加密组件
        self._initialize_encryption()


class PrivacyRiskAssessor:
    """
    隐私风险评估器
    
    动态评估用户表示的隐私风险，并提供保护建议
    """
    
    def __init__(self, config: PrivacyConfig):
        self.config = config
        self.risk_history = []
    
    def assess_privacy_risk(
        self, 
        user_repr: torch.Tensor,
        original_sequence_length: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        评估隐私风险
        
        Args:
            user_repr: 用户表示向量
            original_sequence_length: 原始序列长度（可选）
            
        Returns:
            风险评估结果
        """
        risk_factors = []
        risk_score = 0.0
        
        # 1. 检查表示向量的范数
        repr_norm = torch.norm(user_repr, p=2).item()
        if repr_norm > self.config.max_norm * 0.8:
            risk_factors.append('high_representation_norm')
            risk_score += 0.3
        
        # 2. 检查表示向量的稀疏性
        sparsity = (user_repr == 0).float().mean().item()
        if sparsity < 0.1:  # 非常稠密
            risk_factors.append('dense_representation')
            risk_score += 0.2
        
        # 3. 检查表示向量的方差
        repr_var = torch.var(user_repr).item()
        if repr_var > 5.0:  # 高方差可能包含更多信息
            risk_factors.append('high_variance')
            risk_score += 0.2
        
        # 4. 基于序列长度的风险评估
        if original_sequence_length and original_sequence_length > 100:
            risk_factors.append('long_sequence')
            risk_score += 0.1
        
        # 确定风险级别
        if risk_score >= 0.6:
            risk_level = 'high'
        elif risk_score >= 0.3:
            risk_level = 'medium'
        else:
            risk_level = 'low'
        
        # 生成建议
        recommendations = self._generate_recommendations(risk_factors, risk_level)
        
        risk_assessment = {
            'risk_level': risk_level,
            'risk_score': risk_score,
            'risk_factors': risk_factors,
            'recommendations': recommendations,
            'timestamp': time.time()
        }
        
        # 记录风险历史
        self.risk_history.append(risk_assessment)
        
        return risk_assessment
    
    def _generate_recommendations(self, risk_factors: List[str], risk_level: str) -> List[str]:
        """生成隐私保护建议"""
        recommendations = []
        
        if 'high_representation_norm' in risk_factors:
            recommendations.append('apply_stronger_norm_constraint')
        
        if 'dense_representation' in risk_factors:
            recommendations.append('consider_sparsity_regularization')
        
        if 'high_variance' in risk_factors:
            recommendations.append('increase_noise_level')
        
        if 'long_sequence' in risk_factors:
            recommendations.append('consider_sequence_truncation')
        
        if risk_level == 'high':
            recommendations.append('upgrade_to_higher_privacy_level')
        
        return recommendations


class EdgeCloudPrivacyProtector:
    """
    端云协同隐私保护器
    
    整合差分隐私、安全通信和风险评估，提供完整的隐私保护解决方案
    """
    
    def __init__(self, config: PrivacyConfig):
        self.config = config
        self.dp_protector = DifferentialPrivacyProtector(config)
        self.communicator = SecureCommunicator(config)
        self.risk_assessor = PrivacyRiskAssessor(config)
    
    def protect_and_transmit(
        self, 
        user_repr: torch.Tensor, 
        user_id: str,
        original_sequence_length: Optional[int] = None
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        保护并传输用户表示
        
        Args:
            user_repr: 原始用户表示
            user_id: 用户ID
            original_sequence_length: 原始序列长度
            
        Returns:
            (加密传输包, 隐私风险评估)
        """
        # 1. 隐私风险评估
        risk_assessment = self.risk_assessor.assess_privacy_risk(
            user_repr, original_sequence_length
        )
        
        # 2. 根据风险级别调整隐私保护
        protected_repr = self._apply_privacy_protection(user_repr, risk_assessment)
        
        # 3. 加密传输
        encrypted_package = self.communicator.encrypt_user_representation(
            protected_repr, user_id
        )
        
        return encrypted_package, risk_assessment
    
    def _apply_privacy_protection(
        self, 
        user_repr: torch.Tensor, 
        risk_assessment: Dict[str, Any]
    ) -> torch.Tensor:
        """根据风险评估应用隐私保护"""
        risk_level = risk_assessment['risk_level']
        
        if risk_level == 'high':
            # 高风险：使用高斯噪声 + 更强的范数约束
            protected_repr = self.dp_protector.add_gaussian_noise(user_repr)
        elif risk_level == 'medium':
            # 中等风险：使用拉普拉斯噪声
            protected_repr = self.dp_protector.add_laplace_noise(user_repr)
        else:
            # 低风险：轻微噪声
            noise_scale = self.dp_protector.noise_scale * 0.5
            noise = torch.randn_like(user_repr) * noise_scale
            protected_repr = user_repr + noise
            protected_repr = self.dp_protector._apply_norm_constraint(protected_repr)
        
        return protected_repr
