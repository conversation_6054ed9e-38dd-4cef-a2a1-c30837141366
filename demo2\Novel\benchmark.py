#!/usr/bin/env python3
"""
Novel推荐系统性能基准测试脚本

测试推理延迟、吞吐量等性能指标。

使用方法:
    python benchmark.py --model_type collaborative --dataset Movies_and_TV --batch_size 1 --num_samples 1000
"""

import argparse
import time
import logging
import torch
import numpy as np
from pathlib import Path
import sys
from typing import List, Dict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from models.client.cf_srec import CFSRec
from model import create_recommendation_system

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Novel推荐系统性能基准测试')
    
    # 模型配置
    parser.add_argument('--model_type', type=str, required=True,
                       choices=['cf_srec', 'collaborative', 'edge'],
                       help='模型类型')
    parser.add_argument('--dataset', type=str, required=True,
                       help='数据集名称')
    
    # 测试配置
    parser.add_argument('--batch_size', type=int, default=1,
                       help='批次大小')
    parser.add_argument('--num_samples', type=int, default=1000,
                       help='测试样本数量')
    parser.add_argument('--warmup_samples', type=int, default=100,
                       help='预热样本数量')
    parser.add_argument('--top_k', type=int, default=10,
                       help='推荐数量')
    
    # 设备配置
    parser.add_argument('--device', type=str, default='cpu',
                       help='测试设备')
    
    # 输出配置
    parser.add_argument('--output_file', type=str, default=None,
                       help='结果输出文件')
    parser.add_argument('--detailed_log', action='store_true',
                       help='输出详细日志')
    
    return parser.parse_args()


def generate_test_data(num_samples: int, max_seq_length: int = 50) -> List[List[int]]:
    """生成测试数据"""
    test_data = []
    
    for _ in range(num_samples):
        # 生成随机序列长度
        seq_length = np.random.randint(5, max_seq_length)
        
        # 生成随机用户历史
        user_history = np.random.randint(1, 10000, seq_length).tolist()
        test_data.append(user_history)
    
    return test_data


def benchmark_cf_srec(args) -> Dict:
    """基准测试CF-SRec模型"""
    logger.info("开始CF-SRec模型基准测试")
    
    # 创建模型
    config = {
        'item_num': 10000,
        'hidden_size': 64,
        'max_seq_length': 50,
        'num_attention_heads': 2,
        'num_hidden_layers': 2,
        'dropout_prob': 0.1,
        'user_repr_dim': 64
    }
    
    model = CFSRec(config)
    model.eval()
    
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    model.to(device)
    
    # 生成测试数据
    test_data = generate_test_data(args.num_samples + args.warmup_samples)
    
    # 预热
    logger.info(f"预热阶段: {args.warmup_samples} 个样本")
    warmup_data = test_data[:args.warmup_samples]
    
    with torch.no_grad():
        for user_history in warmup_data:
            _ = model.recommend(user_history, top_k=args.top_k)
    
    # 正式测试
    logger.info(f"正式测试: {args.num_samples} 个样本")
    test_samples = test_data[args.warmup_samples:]
    
    latencies = []
    
    with torch.no_grad():
        for i, user_history in enumerate(test_samples):
            start_time = time.time()
            recommendations = model.recommend(user_history, top_k=args.top_k)
            end_time = time.time()
            
            latency = (end_time - start_time) * 1000  # 转换为毫秒
            latencies.append(latency)
            
            if args.detailed_log and (i + 1) % 100 == 0:
                logger.info(f"已测试 {i + 1} 个样本，当前延迟: {latency:.2f}ms")
    
    # 计算统计信息
    results = {
        'model_type': 'cf_srec',
        'num_samples': args.num_samples,
        'avg_latency_ms': np.mean(latencies),
        'min_latency_ms': np.min(latencies),
        'max_latency_ms': np.max(latencies),
        'p50_latency_ms': np.percentile(latencies, 50),
        'p95_latency_ms': np.percentile(latencies, 95),
        'p99_latency_ms': np.percentile(latencies, 99),
        'throughput_qps': 1000 / np.mean(latencies),
        'device': str(device)
    }
    
    return results


def benchmark_collaborative(args) -> Dict:
    """基准测试协同推荐系统"""
    logger.info("开始协同推荐系统基准测试")
    
    # 创建推荐系统
    rec_system = create_recommendation_system(
        device=args.device,
        inference_mode='edge',  # 使用端侧模式避免网络延迟
        quick_test=True
    )
    
    # 生成测试数据
    test_data = generate_test_data(args.num_samples + args.warmup_samples)
    
    # 预热
    logger.info(f"预热阶段: {args.warmup_samples} 个样本")
    warmup_data = test_data[:args.warmup_samples]
    
    for user_history in warmup_data:
        _ = rec_system.recommend(user_history, top_k=args.top_k)
    
    # 正式测试
    logger.info(f"正式测试: {args.num_samples} 个样本")
    test_samples = test_data[args.warmup_samples:]
    
    latencies = []
    
    for i, user_history in enumerate(test_samples):
        start_time = time.time()
        recommendations = rec_system.recommend(user_history, top_k=args.top_k)
        end_time = time.time()
        
        latency = (end_time - start_time) * 1000  # 转换为毫秒
        latencies.append(latency)
        
        if args.detailed_log and (i + 1) % 100 == 0:
            logger.info(f"已测试 {i + 1} 个样本，当前延迟: {latency:.2f}ms")
    
    # 计算统计信息
    results = {
        'model_type': 'collaborative',
        'num_samples': args.num_samples,
        'avg_latency_ms': np.mean(latencies),
        'min_latency_ms': np.min(latencies),
        'max_latency_ms': np.max(latencies),
        'p50_latency_ms': np.percentile(latencies, 50),
        'p95_latency_ms': np.percentile(latencies, 95),
        'p99_latency_ms': np.percentile(latencies, 99),
        'throughput_qps': 1000 / np.mean(latencies),
        'device': args.device
    }
    
    return results


def print_results(results: Dict):
    """打印测试结果"""
    logger.info("=" * 60)
    logger.info("性能基准测试结果")
    logger.info("=" * 60)
    logger.info(f"模型类型: {results['model_type']}")
    logger.info(f"测试样本数: {results['num_samples']}")
    logger.info(f"设备: {results['device']}")
    logger.info("-" * 60)
    logger.info(f"平均延迟: {results['avg_latency_ms']:.2f} ms")
    logger.info(f"最小延迟: {results['min_latency_ms']:.2f} ms")
    logger.info(f"最大延迟: {results['max_latency_ms']:.2f} ms")
    logger.info(f"P50延迟: {results['p50_latency_ms']:.2f} ms")
    logger.info(f"P95延迟: {results['p95_latency_ms']:.2f} ms")
    logger.info(f"P99延迟: {results['p99_latency_ms']:.2f} ms")
    logger.info(f"吞吐量: {results['throughput_qps']:.2f} QPS")
    logger.info("=" * 60)


def save_results(results: Dict, output_file: str):
    """保存测试结果"""
    import json
    
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"结果已保存到: {output_file}")


def main():
    """主函数"""
    args = parse_args()
    
    logger.info(f"开始性能基准测试")
    logger.info(f"模型类型: {args.model_type}")
    logger.info(f"数据集: {args.dataset}")
    logger.info(f"设备: {args.device}")
    logger.info(f"测试样本数: {args.num_samples}")
    
    try:
        if args.model_type == 'cf_srec':
            results = benchmark_cf_srec(args)
        elif args.model_type in ['collaborative', 'edge']:
            results = benchmark_collaborative(args)
        else:
            raise ValueError(f"未知的模型类型: {args.model_type}")
        
        # 打印结果
        print_results(results)
        
        # 保存结果
        if args.output_file:
            save_results(results, args.output_file)
        
    except Exception as e:
        logger.error(f"基准测试失败: {e}")
        raise


if __name__ == '__main__':
    main()
