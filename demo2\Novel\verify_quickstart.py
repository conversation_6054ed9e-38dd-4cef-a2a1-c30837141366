#!/usr/bin/env python3
"""
QUICKSTART.md 验证脚本

验证QUICKSTART.md中的所有命令和流程是否正常工作。
包含单GPU、多GPU和双3090优化配置的完整测试。

使用方法:
    python verify_quickstart.py
    python verify_quickstart.py --dual-3090  # 双3090完整测试
"""

import subprocess
import sys
import logging
import time
import argparse
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def run_command(command: str, description: str, timeout: int = 30) -> bool:
    """运行命令并检查结果"""
    logger.info(f"测试: {description}")
    logger.info(f"命令: {command}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        
        if result.returncode == 0:
            logger.info(f"✅ {description} - 成功")
            return True
        else:
            logger.error(f"❌ {description} - 失败")
            logger.error(f"错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"❌ {description} - 超时")
        return False
    except Exception as e:
        logger.error(f"❌ {description} - 异常: {e}")
        return False


def verify_environment():
    """验证环境配置"""
    logger.info("=" * 60)
    logger.info("1. 验证环境配置")
    logger.info("=" * 60)
    
    tests = [
        ('python -c "import torch; print(f\'PyTorch: {torch.__version__}\')"', 
         "PyTorch安装检查"),
        ('python -c "import transformers; print(f\'Transformers: {transformers.__version__}\')"', 
         "Transformers安装检查"),
        ('python -c "from models.client.cf_srec import CFSRec; print(\'CF-SRec导入成功\')"', 
         "CF-SRec模块导入"),
        ('python -c "from model import create_recommendation_system; print(\'推荐系统导入成功\')"', 
         "推荐系统导入"),
    ]
    
    results = []
    for command, description in tests:
        results.append(run_command(command, description))
    
    return all(results)


def verify_cf_srec_model():
    """验证CF-SRec模型功能"""
    logger.info("=" * 60)
    logger.info("2. 验证CF-SRec模型功能")
    logger.info("=" * 60)
    
    test_script = '''
from models.client.cf_srec import CFSRec

# 创建模型
config = {
    "item_num": 10000,
    "hidden_size": 64,
    "max_seq_length": 50,
    "num_attention_heads": 2,
    "num_hidden_layers": 2,
    "dropout_prob": 0.1,
    "user_repr_dim": 64
}

cf_srec = CFSRec(config)
print("✅ CF-SRec模型创建成功")

# 测试推荐
user_history = [1, 5, 23, 45, 67]
recommendations = cf_srec.recommend(user_history, top_k=5)
print(f"✅ CF-SRec推荐成功: {recommendations}")

# 测试用户表示
user_repr = cf_srec.get_user_representation(user_history)
print(f"✅ 用户表示生成成功，维度: {user_repr.shape}")

print("✅ CF-SRec模型所有功能正常")
'''
    
    return run_command(f'python -c "{test_script}"', "CF-SRec模型功能测试", timeout=60)


def verify_recommendation_system():
    """验证推荐系统功能"""
    logger.info("=" * 60)
    logger.info("3. 验证推荐系统功能")
    logger.info("=" * 60)
    
    test_script = '''
from model import create_recommendation_system

# 创建推荐系统
rec_system = create_recommendation_system(
    device="cpu",
    inference_mode="edge",
    quick_test=True
)
print("✅ 推荐系统创建成功")

# 测试推荐
user_history = [1, 5, 23, 45, 67]
recommendations = rec_system.recommend(user_history, top_k=10)
print(f"✅ 推荐生成成功: {recommendations}")

print("✅ 推荐系统所有功能正常")
'''
    
    return run_command(f'python -c "{test_script}"', "推荐系统功能测试", timeout=60)


def verify_benchmark_tools():
    """验证基准测试工具"""
    logger.info("=" * 60)
    logger.info("4. 验证基准测试工具")
    logger.info("=" * 60)
    
    tests = [
        ('python benchmark.py --model_type cf_srec --dataset Movies_and_TV --num_samples 10', 
         "CF-SRec基准测试"),
        ('python benchmark.py --model_type collaborative --dataset Movies_and_TV --num_samples 10', 
         "协同系统基准测试"),
    ]
    
    results = []
    for command, description in tests:
        results.append(run_command(command, description, timeout=60))
    
    return all(results)


def verify_evaluation_tools():
    """验证评估工具"""
    logger.info("=" * 60)
    logger.info("5. 验证评估工具")
    logger.info("=" * 60)
    
    # 创建一个虚拟模型文件
    dummy_model_path = "dummy_model.pth"
    create_dummy_model = f'''
import torch
from models.client.cf_srec import CFSRec

config = {{"item_num": 10000, "hidden_size": 64}}
model = CFSRec(config)
torch.save(model.state_dict(), "{dummy_model_path}")
print("虚拟模型文件已创建")
'''
    
    if run_command(f'python -c "{create_dummy_model}"', "创建虚拟模型文件"):
        result = run_command(
            f'python evaluate.py --model_type cf_srec --dataset Movies_and_TV --model_path {dummy_model_path} --eval_users 10',
            "评估工具测试",
            timeout=60
        )
        
        # 清理虚拟文件
        try:
            Path(dummy_model_path).unlink()
        except:
            pass
        
        return result
    
    return False


def verify_experiment_runner():
    """验证实验运行器"""
    logger.info("=" * 60)
    logger.info("6. 验证实验运行器")
    logger.info("=" * 60)
    
    # 测试实验运行器的帮助信息
    return run_command(
        'python run_experiment.py --help',
        "实验运行器帮助信息",
        timeout=30
    )


def main():
    """主函数"""
    logger.info("🚀 开始验证QUICKSTART.md流程")
    logger.info("=" * 80)
    
    # 验证步骤
    verification_steps = [
        ("环境配置", verify_environment),
        ("CF-SRec模型", verify_cf_srec_model),
        ("推荐系统", verify_recommendation_system),
        ("基准测试工具", verify_benchmark_tools),
        ("评估工具", verify_evaluation_tools),
        ("实验运行器", verify_experiment_runner),
    ]
    
    results = {}
    
    for step_name, verify_func in verification_steps:
        try:
            results[step_name] = verify_func()
        except Exception as e:
            logger.error(f"验证 {step_name} 时发生异常: {e}")
            results[step_name] = False
    
    # 输出总结
    logger.info("=" * 80)
    logger.info("📊 验证结果总结")
    logger.info("=" * 80)
    
    all_passed = True
    for step_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        logger.info(f"{step_name}: {status}")
        if not passed:
            all_passed = False
    
    logger.info("=" * 80)
    
    if all_passed:
        logger.info("🎉 所有验证步骤都通过了！QUICKSTART.md 流程正常工作。")
        sys.exit(0)
    else:
        logger.error("❌ 部分验证步骤失败。请检查上述错误信息。")
        sys.exit(1)


def verify_gpu_environment():
    """验证GPU环境（双3090测试）"""
    logger.info("=" * 60)
    logger.info("7. 验证GPU环境")
    logger.info("=" * 60)

    tests = [
        ('python -c "from utils import GPUManager; gpu_manager = GPUManager(); print(f\'GPU可用: {gpu_manager.check_gpu_environment()}\')"',
         "GPU管理器测试"),
        ('python -c "import torch; print(f\'CUDA可用: {torch.cuda.is_available()}\'); print(f\'GPU数量: {torch.cuda.device_count()}\')"',
         "PyTorch GPU支持"),
    ]

    results = []
    for command, description in tests:
        results.append(run_command(command, description))

    return all(results)


def verify_multi_gpu_training():
    """验证多GPU训练功能"""
    logger.info("=" * 60)
    logger.info("8. 验证多GPU训练功能")
    logger.info("=" * 60)

    # 快速多GPU训练测试
    command = '''python run_experiment.py \
    --stage pretrain_cf_srec \
    --dataset Movies_and_TV \
    --device cpu \
    --multi_gpu \
    --batch-size 32 \
    --num-epochs 1 \
    --hidden_units 32 \
    --num_blocks 1 \
    --maxlen 20 \
    --experiment-name "verify_multi_gpu"'''

    return run_command(command, "多GPU训练测试", timeout=120)


def verify_dual_3090_optimized():
    """验证双3090优化配置"""
    logger.info("=" * 60)
    logger.info("9. 验证双3090优化配置")
    logger.info("=" * 60)

    # 双3090优化配置测试（使用较小参数进行快速验证）
    command = '''python run_experiment.py \
    --stage pretrain_cf_srec \
    --dataset Movies_and_TV \
    --device cuda \
    --multi_gpu \
    --batch-size 64 \
    --num-epochs 1 \
    --hidden_units 64 \
    --num_blocks 2 \
    --maxlen 50 \
    --fp16 \
    --experiment-name "verify_dual_3090"'''

    return run_command(command, "双3090优化配置测试", timeout=180)


def main_with_gpu_tests():
    """包含GPU测试的主函数"""
    parser = argparse.ArgumentParser(description='QUICKSTART.md验证脚本')
    parser.add_argument('--dual-3090', action='store_true',
                       help='运行完整的双3090测试套件')
    parser.add_argument('--gpu-only', action='store_true',
                       help='只运行GPU相关测试')
    args = parser.parse_args()

    logger.info("🧪 QUICKSTART.md 验证开始")
    logger.info("=" * 80)

    # 基础验证步骤
    basic_steps = [
        ("环境配置", verify_environment),
        ("CF-SRec模型", verify_cf_srec_model),
        ("推荐系统", verify_recommendation_system),
        ("基准测试工具", verify_benchmark_tools),
        ("评估工具", verify_evaluation_tools),
        ("实验运行器", verify_experiment_runner),
    ]

    # GPU验证步骤
    gpu_steps = [
        ("GPU环境", verify_gpu_environment),
        ("多GPU训练", verify_multi_gpu_training),
    ]

    # 双3090优化步骤
    dual_3090_steps = [
        ("双3090优化配置", verify_dual_3090_optimized),
    ]

    # 选择要运行的测试
    if args.gpu_only:
        verification_steps = gpu_steps
        logger.info("🔧 运行GPU专项测试")
    elif args.dual_3090:
        verification_steps = basic_steps + gpu_steps + dual_3090_steps
        logger.info("🚀 运行完整双3090测试套件")
    else:
        verification_steps = basic_steps + gpu_steps
        logger.info("📋 运行标准验证测试")

    results = {}
    start_time = time.time()

    for step_name, verify_func in verification_steps:
        step_start = time.time()
        try:
            results[step_name] = verify_func()
        except Exception as e:
            logger.error(f"验证 {step_name} 时发生异常: {e}")
            results[step_name] = False

        step_time = time.time() - step_start
        status = "✅ 通过" if results[step_name] else "❌ 失败"
        logger.info(f"{step_name}: {status} (耗时: {step_time:.1f}s)")

    # 输出总结
    total_time = time.time() - start_time
    logger.info("=" * 80)
    logger.info("📊 验证结果总结")
    logger.info("=" * 80)

    all_passed = True
    for step_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        logger.info(f"{step_name}: {status}")
        if not passed:
            all_passed = False

    logger.info(f"总耗时: {total_time:.1f}s")
    logger.info("=" * 80)

    if all_passed:
        logger.info("🎉 所有验证步骤都通过了！")
        if args.dual_3090:
            logger.info("✅ 双3090服务器配置完美，可以进行大规模训练！")
            logger.info("\n推荐生产环境命令:")
            logger.info("python run_experiment.py --stage pretrain_cf_srec --dataset Movies_and_TV --device cuda --multi_gpu --batch-size 512 --num-epochs 200 --hidden_units 128 --num_blocks 4 --maxlen 128 --fp16")
        else:
            logger.info("✅ QUICKSTART.md 流程正常工作！")
        sys.exit(0)
    else:
        logger.error("❌ 部分验证步骤失败。请检查上述错误信息。")
        sys.exit(1)


if __name__ == '__main__':
    # 检查是否有GPU相关参数
    if len(sys.argv) > 1 and ('--dual-3090' in sys.argv or '--gpu-only' in sys.argv):
        main_with_gpu_tests()
    else:
        main()
