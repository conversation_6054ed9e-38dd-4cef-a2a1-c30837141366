"""
隐私风险评估子模块

本模块提供全面的隐私风险评估和管理：
1. 风险分析：多维度评估隐私泄露风险
2. 风险量化：将风险转化为可量化的指标
3. 自适应控制：根据风险动态调整保护策略
4. 风险监控：实时监控和预警隐私风险

核心功能：
- 多因子风险评估：综合考虑数据、模型、环境等因素
- 风险预测：基于历史数据预测未来风险趋势
- 动态调整：实时调整隐私保护参数
- 风险报告：生成详细的风险评估报告
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass, asdict
import logging
import time
from collections import deque
from abc import ABC, abstractmethod
import json


@dataclass
class RiskMetrics:
    """风险指标"""
    # 基础风险指标
    information_leakage_risk: float = 0.0  # 信息泄露风险
    reconstruction_risk: float = 0.0       # 重构攻击风险
    membership_inference_risk: float = 0.0 # 成员推断风险
    attribute_inference_risk: float = 0.0  # 属性推断风险
    
    # 综合风险指标
    overall_risk_score: float = 0.0        # 总体风险分数
    risk_level: str = 'low'                # 风险级别
    confidence: float = 1.0                # 评估置信度
    
    # 元数据
    timestamp: float = 0.0
    assessment_method: str = ''
    data_characteristics: Dict[str, Any] = None


class RiskAssessmentMethod(ABC):
    """风险评估方法抽象基类"""
    
    @abstractmethod
    def assess_risk(self, data: torch.Tensor, context: Dict[str, Any]) -> RiskMetrics:
        """评估风险"""
        pass
    
    @abstractmethod
    def get_method_name(self) -> str:
        """获取方法名称"""
        pass


class StatisticalRiskAssessment(RiskAssessmentMethod):
    """
    基于统计特性的风险评估
    
    通过分析数据的统计特性来评估隐私风险
    """
    
    def __init__(self, sensitivity_threshold: float = 0.5):
        self.sensitivity_threshold = sensitivity_threshold
        self.logger = logging.getLogger(__name__)
    
    def assess_risk(self, data: torch.Tensor, context: Dict[str, Any]) -> RiskMetrics:
        """基于统计特性评估风险"""
        # 计算数据统计特性
        stats = self._compute_statistics(data)
        
        # 评估各类风险
        info_leak_risk = self._assess_information_leakage(stats)
        recon_risk = self._assess_reconstruction_risk(stats)
        member_risk = self._assess_membership_inference(stats, context)
        attr_risk = self._assess_attribute_inference(stats)
        
        # 计算综合风险
        overall_risk = self._compute_overall_risk([
            info_leak_risk, recon_risk, member_risk, attr_risk
        ])
        
        # 确定风险级别
        risk_level = self._determine_risk_level(overall_risk)
        
        return RiskMetrics(
            information_leakage_risk=info_leak_risk,
            reconstruction_risk=recon_risk,
            membership_inference_risk=member_risk,
            attribute_inference_risk=attr_risk,
            overall_risk_score=overall_risk,
            risk_level=risk_level,
            confidence=0.8,  # 统计方法的置信度
            timestamp=time.time(),
            assessment_method='statistical',
            data_characteristics=stats
        )
    
    def _compute_statistics(self, data: torch.Tensor) -> Dict[str, float]:
        """计算数据统计特性"""
        return {
            'mean': torch.mean(data).item(),
            'std': torch.std(data).item(),
            'var': torch.var(data).item(),
            'norm_l1': torch.norm(data, p=1).item(),
            'norm_l2': torch.norm(data, p=2).item(),
            'norm_inf': torch.norm(data, p=float('inf')).item(),
            'sparsity': (data == 0).float().mean().item(),
            'entropy': self._compute_entropy(data),
            'skewness': self._compute_skewness(data),
            'kurtosis': self._compute_kurtosis(data)
        }
    
    def _compute_entropy(self, data: torch.Tensor) -> float:
        """计算数据熵"""
        # 简化的熵计算
        data_flat = data.flatten()
        unique_values, counts = torch.unique(data_flat, return_counts=True)
        probabilities = counts.float() / len(data_flat)
        entropy = -torch.sum(probabilities * torch.log2(probabilities + 1e-10))
        return entropy.item()
    
    def _compute_skewness(self, data: torch.Tensor) -> float:
        """计算偏度"""
        mean = torch.mean(data)
        std = torch.std(data)
        skewness = torch.mean(((data - mean) / (std + 1e-10)) ** 3)
        return skewness.item()
    
    def _compute_kurtosis(self, data: torch.Tensor) -> float:
        """计算峰度"""
        mean = torch.mean(data)
        std = torch.std(data)
        kurtosis = torch.mean(((data - mean) / (std + 1e-10)) ** 4) - 3
        return kurtosis.item()
    
    def _assess_information_leakage(self, stats: Dict[str, float]) -> float:
        """评估信息泄露风险"""
        # 基于熵和方差评估
        entropy_risk = max(0, 1 - stats['entropy'] / 10.0)  # 熵越低风险越高
        variance_risk = min(1, stats['var'] / 10.0)  # 方差越高风险越高
        
        return (entropy_risk + variance_risk) / 2
    
    def _assess_reconstruction_risk(self, stats: Dict[str, float]) -> float:
        """评估重构攻击风险"""
        # 基于稀疏性和范数评估
        sparsity_risk = 1 - stats['sparsity']  # 稀疏性越低风险越高
        norm_risk = min(1, stats['norm_l2'] / 20.0)  # 范数越大风险越高
        
        return (sparsity_risk + norm_risk) / 2
    
    def _assess_membership_inference(self, stats: Dict[str, float], context: Dict[str, Any]) -> float:
        """评估成员推断风险"""
        # 基于数据分布特性评估
        skew_risk = min(1, abs(stats['skewness']) / 3.0)
        kurt_risk = min(1, abs(stats['kurtosis']) / 5.0)
        
        # 考虑序列长度（如果提供）
        seq_length = context.get('sequence_length', 0)
        length_risk = min(1, seq_length / 200.0) if seq_length > 0 else 0
        
        return (skew_risk + kurt_risk + length_risk) / 3
    
    def _assess_attribute_inference(self, stats: Dict[str, float]) -> float:
        """评估属性推断风险"""
        # 基于数据的可预测性评估
        predictability = 1 - min(1, stats['entropy'] / 8.0)
        concentration = min(1, stats['norm_inf'] / stats['norm_l1']) if stats['norm_l1'] > 0 else 0
        
        return (predictability + concentration) / 2
    
    def _compute_overall_risk(self, individual_risks: List[float]) -> float:
        """计算综合风险分数"""
        # 使用加权平均，可以根据具体应用调整权重
        weights = [0.3, 0.25, 0.25, 0.2]  # 对应四种风险的权重
        return sum(w * r for w, r in zip(weights, individual_risks))
    
    def _determine_risk_level(self, risk_score: float) -> str:
        """确定风险级别"""
        if risk_score >= 0.7:
            return 'high'
        elif risk_score >= 0.4:
            return 'medium'
        else:
            return 'low'
    
    def get_method_name(self) -> str:
        return 'statistical'


class ModelBasedRiskAssessment(RiskAssessmentMethod):
    """
    基于模型的风险评估
    
    使用机器学习模型来评估隐私风险
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        # 这里可以加载预训练的风险评估模型
        self.risk_model = None
    
    def assess_risk(self, data: torch.Tensor, context: Dict[str, Any]) -> RiskMetrics:
        """基于模型评估风险"""
        # 简化实现：使用启发式规则模拟模型预测
        features = self._extract_features(data, context)
        
        # 模拟模型预测（实际应用中应该使用训练好的模型）
        predictions = self._simulate_model_prediction(features)
        
        return RiskMetrics(
            information_leakage_risk=predictions['info_leak'],
            reconstruction_risk=predictions['reconstruction'],
            membership_inference_risk=predictions['membership'],
            attribute_inference_risk=predictions['attribute'],
            overall_risk_score=predictions['overall'],
            risk_level=self._determine_risk_level(predictions['overall']),
            confidence=0.9,  # 模型方法的置信度
            timestamp=time.time(),
            assessment_method='model_based',
            data_characteristics=features
        )
    
    def _extract_features(self, data: torch.Tensor, context: Dict[str, Any]) -> Dict[str, float]:
        """提取特征"""
        features = {
            'data_size': data.numel(),
            'data_dim': len(data.shape),
            'mean_abs': torch.mean(torch.abs(data)).item(),
            'max_abs': torch.max(torch.abs(data)).item(),
            'zero_ratio': (data == 0).float().mean().item(),
        }
        
        # 添加上下文特征
        features.update({
            'sequence_length': context.get('sequence_length', 0),
            'user_activity': context.get('user_activity_level', 0.5),
            'data_sensitivity': context.get('data_sensitivity', 0.5)
        })
        
        return features
    
    def _simulate_model_prediction(self, features: Dict[str, float]) -> Dict[str, float]:
        """模拟模型预测"""
        # 简化的预测逻辑
        base_risk = features['mean_abs'] * 0.3 + features['max_abs'] * 0.2
        
        return {
            'info_leak': min(1.0, base_risk * 1.2),
            'reconstruction': min(1.0, base_risk * 0.8 + features['zero_ratio'] * 0.3),
            'membership': min(1.0, base_risk * 0.9 + features['sequence_length'] / 500.0),
            'attribute': min(1.0, base_risk * 1.1),
            'overall': min(1.0, base_risk)
        }
    
    def _determine_risk_level(self, risk_score: float) -> str:
        """确定风险级别"""
        if risk_score >= 0.6:
            return 'high'
        elif risk_score >= 0.3:
            return 'medium'
        else:
            return 'low'
    
    def get_method_name(self) -> str:
        return 'model_based'


class PrivacyRiskAnalyzer:
    """
    隐私风险分析器
    
    整合多种风险评估方法，提供综合的风险分析
    """
    
    def __init__(self, assessment_methods: Optional[List[RiskAssessmentMethod]] = None):
        self.assessment_methods = assessment_methods or [
            StatisticalRiskAssessment(),
            ModelBasedRiskAssessment()
        ]
        self.risk_history = deque(maxlen=1000)
        self.logger = logging.getLogger(__name__)
    
    def analyze_risk(
        self, 
        data: torch.Tensor, 
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, RiskMetrics]:
        """
        综合风险分析
        
        Args:
            data: 待分析的数据
            context: 上下文信息
            
        Returns:
            各种方法的风险评估结果
        """
        context = context or {}
        risk_results = {}
        
        for method in self.assessment_methods:
            try:
                risk_metrics = method.assess_risk(data, context)
                risk_results[method.get_method_name()] = risk_metrics
                
                # 记录到历史
                self.risk_history.append({
                    'method': method.get_method_name(),
                    'metrics': risk_metrics,
                    'data_shape': list(data.shape),
                    'context': context
                })
                
            except Exception as e:
                self.logger.error(f"风险评估方法 {method.get_method_name()} 失败: {e}")
        
        return risk_results
    
    def get_consensus_risk(self, risk_results: Dict[str, RiskMetrics]) -> RiskMetrics:
        """
        获取一致性风险评估
        
        Args:
            risk_results: 各方法的风险评估结果
            
        Returns:
            一致性风险指标
        """
        if not risk_results:
            return RiskMetrics()
        
        # 计算各指标的平均值
        info_leak_risks = [r.information_leakage_risk for r in risk_results.values()]
        recon_risks = [r.reconstruction_risk for r in risk_results.values()]
        member_risks = [r.membership_inference_risk for r in risk_results.values()]
        attr_risks = [r.attribute_inference_risk for r in risk_results.values()]
        overall_risks = [r.overall_risk_score for r in risk_results.values()]
        confidences = [r.confidence for r in risk_results.values()]
        
        # 加权平均（根据置信度）
        total_confidence = sum(confidences)
        if total_confidence > 0:
            weights = [c / total_confidence for c in confidences]
        else:
            weights = [1.0 / len(confidences)] * len(confidences)
        
        consensus_metrics = RiskMetrics(
            information_leakage_risk=sum(w * r for w, r in zip(weights, info_leak_risks)),
            reconstruction_risk=sum(w * r for w, r in zip(weights, recon_risks)),
            membership_inference_risk=sum(w * r for w, r in zip(weights, member_risks)),
            attribute_inference_risk=sum(w * r for w, r in zip(weights, attr_risks)),
            overall_risk_score=sum(w * r for w, r in zip(weights, overall_risks)),
            confidence=np.mean(confidences),
            timestamp=time.time(),
            assessment_method='consensus'
        )
        
        # 确定一致性风险级别
        consensus_metrics.risk_level = self._determine_consensus_risk_level(
            [r.risk_level for r in risk_results.values()]
        )
        
        return consensus_metrics
    
    def _determine_consensus_risk_level(self, risk_levels: List[str]) -> str:
        """确定一致性风险级别"""
        level_counts = {'low': 0, 'medium': 0, 'high': 0}
        for level in risk_levels:
            level_counts[level] += 1
        
        # 选择最多的级别，如果平局则选择更高的级别
        if level_counts['high'] > 0:
            return 'high'
        elif level_counts['medium'] >= level_counts['low']:
            return 'medium'
        else:
            return 'low'
    
    def get_risk_trends(self, window_size: int = 50) -> Dict[str, Any]:
        """
        获取风险趋势分析
        
        Args:
            window_size: 分析窗口大小
            
        Returns:
            风险趋势信息
        """
        if len(self.risk_history) < 2:
            return {'trend': 'insufficient_data'}
        
        recent_history = list(self.risk_history)[-window_size:]
        
        # 提取风险分数时间序列
        risk_scores = [h['metrics'].overall_risk_score for h in recent_history]
        timestamps = [h['metrics'].timestamp for h in recent_history]
        
        # 计算趋势
        if len(risk_scores) >= 2:
            trend_slope = np.polyfit(range(len(risk_scores)), risk_scores, 1)[0]
            
            if trend_slope > 0.01:
                trend = 'increasing'
            elif trend_slope < -0.01:
                trend = 'decreasing'
            else:
                trend = 'stable'
        else:
            trend = 'stable'
        
        return {
            'trend': trend,
            'trend_slope': trend_slope if len(risk_scores) >= 2 else 0,
            'current_risk': risk_scores[-1] if risk_scores else 0,
            'average_risk': np.mean(risk_scores),
            'risk_volatility': np.std(risk_scores),
            'sample_count': len(recent_history)
        }


class AdaptivePrivacyController:
    """
    自适应隐私控制器
    
    根据风险评估结果动态调整隐私保护策略
    """
    
    def __init__(self, risk_analyzer: PrivacyRiskAnalyzer):
        self.risk_analyzer = risk_analyzer
        self.control_history = []
        self.logger = logging.getLogger(__name__)
    
    def adjust_privacy_parameters(
        self, 
        current_params: Dict[str, float],
        risk_metrics: RiskMetrics
    ) -> Dict[str, float]:
        """
        根据风险调整隐私参数
        
        Args:
            current_params: 当前隐私参数
            risk_metrics: 风险评估结果
            
        Returns:
            调整后的隐私参数
        """
        adjusted_params = current_params.copy()
        
        # 根据风险级别调整参数
        if risk_metrics.risk_level == 'high':
            # 高风险：增强隐私保护
            adjusted_params['epsilon'] = current_params.get('epsilon', 1.0) * 0.5
            adjusted_params['noise_multiplier'] = current_params.get('noise_multiplier', 1.0) * 2.0
            adjusted_params['clipping_bound'] = current_params.get('clipping_bound', 1.0) * 0.8
            
        elif risk_metrics.risk_level == 'low':
            # 低风险：适度放松保护以提高效用
            adjusted_params['epsilon'] = min(4.0, current_params.get('epsilon', 1.0) * 1.2)
            adjusted_params['noise_multiplier'] = max(0.5, current_params.get('noise_multiplier', 1.0) * 0.9)
            adjusted_params['clipping_bound'] = min(2.0, current_params.get('clipping_bound', 1.0) * 1.1)
        
        # 记录调整历史
        control_record = {
            'timestamp': time.time(),
            'original_params': current_params,
            'adjusted_params': adjusted_params,
            'risk_metrics': asdict(risk_metrics),
            'adjustment_reason': f'risk_level_{risk_metrics.risk_level}'
        }
        self.control_history.append(control_record)
        
        self.logger.info(f"隐私参数调整: 风险级别={risk_metrics.risk_level}, "
                        f"ε: {current_params.get('epsilon', 1.0):.3f} -> {adjusted_params['epsilon']:.3f}")
        
        return adjusted_params
    
    def get_control_statistics(self) -> Dict[str, Any]:
        """获取控制统计信息"""
        if not self.control_history:
            return {'adjustment_count': 0}
        
        # 统计调整次数
        adjustment_reasons = [record['adjustment_reason'] for record in self.control_history]
        reason_counts = {}
        for reason in adjustment_reasons:
            reason_counts[reason] = reason_counts.get(reason, 0) + 1
        
        # 计算参数变化趋势
        epsilon_changes = []
        for record in self.control_history:
            original_eps = record['original_params'].get('epsilon', 1.0)
            adjusted_eps = record['adjusted_params'].get('epsilon', 1.0)
            epsilon_changes.append(adjusted_eps / original_eps)
        
        return {
            'adjustment_count': len(self.control_history),
            'adjustment_reasons': reason_counts,
            'average_epsilon_change_ratio': np.mean(epsilon_changes) if epsilon_changes else 1.0,
            'latest_adjustment': self.control_history[-1] if self.control_history else None
        }
