"""
端云协同推荐系统主集成模块

本模块整合所有端云协同组件，提供完整的推荐系统解决方案：
1. 端侧CF-SRec模型：本地序列建模和隐私保护
2. 云端LLM模型：深度推荐优化和个性化
3. 安全通信：加密的端云数据传输
4. 知识蒸馏：持续的模型协同进化

核心功能：
- 统一的推荐接口：支持端侧、云端和协同推荐
- 自动负载均衡：根据网络和计算资源智能调度
- 性能监控：全面的系统性能和质量监控
- 配置管理：灵活的系统配置和参数调优
"""

import asyncio
import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass, asdict
import logging
import time
import json
from pathlib import Path
from enum import Enum

from .edge_cf_srec import EdgeCFSRec, create_edge_cf_srec
from .cloud_llm_enhanced import CloudLLMEnhanced, create_cloud_llm_enhanced
from .edge_cloud_communication import EdgeCloudCommunicator, create_communicator
from .knowledge_distillation import EdgeCloudDistillationFramework, create_distillation_framework, create_bidirectional_distillation_framework
from privacy import PrivacyConfig


class RecommendationMode(Enum):
    """推荐模式"""
    EDGE_ONLY = "edge_only"          # 仅端侧推荐
    CLOUD_ONLY = "cloud_only"        # 仅云端推荐
    COLLABORATIVE = "collaborative"   # 端云协同推荐
    ADAPTIVE = "adaptive"            # 自适应模式选择


@dataclass
class SystemConfig:
    """系统配置"""
    # 模型配置
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
    rec_pre_trained_data: str = "Movies_and_TV"
    llm_model: str = "llama-3b"
    recsys_model: str = "sasrec"
    maxlen: int = 128
    
    # 推荐配置
    default_recommendation_mode: RecommendationMode = RecommendationMode.COLLABORATIVE
    default_num_recommendations: int = 10
    edge_recommendation_threshold: float = 0.8  # 端侧推荐置信度阈值
    
    # 通信配置
    cloud_server_url: str = "https://cloud-llm-server.example.com"
    api_key: str = "your-api-key-here"
    communication_timeout: float = 30.0
    
    # 隐私配置
    privacy_level: str = "high"
    enable_differential_privacy: bool = True
    user_repr_dim: int = 64
    
    # 蒸馏配置
    enable_knowledge_distillation: bool = True
    enable_bidirectional_distillation: bool = True  # 启用双向蒸馏
    distillation_interval: int = 100  # 每100次推荐后进行一次蒸馏
    
    # 性能配置
    enable_caching: bool = True
    cache_size: int = 1000
    performance_monitoring: bool = True


@dataclass
class SystemMetrics:
    """系统性能指标"""
    total_recommendations: int = 0
    edge_recommendations: int = 0
    cloud_recommendations: int = 0
    collaborative_recommendations: int = 0
    
    average_response_time: float = 0.0
    edge_response_time: float = 0.0
    cloud_response_time: float = 0.0
    
    recommendation_accuracy: float = 0.0
    privacy_protection_level: float = 1.0
    
    cache_hit_rate: float = 0.0
    knowledge_distillation_count: int = 0
    
    system_uptime: float = 0.0
    last_update_time: float = 0.0


class EdgeCloudRecommendationSystem:
    """
    端云协同推荐系统
    
    整合端侧CF-SRec、云端LLM、安全通信和知识蒸馏，
    提供完整的端云协同推荐解决方案
    """
    
    def __init__(self, config: Optional[SystemConfig] = None):
        """
        初始化端云协同推荐系统
        
        Args:
            config: 系统配置
        """
        self.config = config or SystemConfig()
        self.logger = self._setup_logger()
        
        # 系统状态
        self.is_initialized = False
        self.start_time = time.time()
        self.metrics = SystemMetrics()
        
        # 核心组件（延迟初始化）
        self.edge_model: Optional[EdgeCFSRec] = None
        self.cloud_model: Optional[CloudLLMEnhanced] = None
        self.communicator: Optional[EdgeCloudCommunicator] = None
        self.distillation_framework: Optional[EdgeCloudDistillationFramework] = None
        
        # 缓存和队列
        self.recommendation_cache = {}
        self.distillation_queue = []
        
        self.logger.info("端云协同推荐系统创建完成")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志器"""
        logger = logging.getLogger(f"{__name__}.EdgeCloudSystem")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    async def initialize(self):
        """初始化系统组件"""
        if self.is_initialized:
            self.logger.warning("系统已经初始化")
            return
        
        self.logger.info("开始初始化端云协同推荐系统...")
        
        try:
            # 创建模型配置参数
            args = self._create_model_args()
            
            # 初始化端侧模型
            self.logger.info("初始化端侧CF-SRec模型...")
            self.edge_model = create_edge_cf_srec(args, self.config.privacy_level)
            
            # 初始化云端模型
            self.logger.info("初始化云端LLM增强模型...")
            self.cloud_model = create_cloud_llm_enhanced(args, self.config.privacy_level)
            
            # 初始化通信器
            self.logger.info("初始化端云通信器...")
            self.communicator = await create_communicator(
                self.config.cloud_server_url,
                self.config.api_key,
                self.config.privacy_level
            )
            
            # 初始化知识蒸馏框架
            if self.config.enable_knowledge_distillation:
                if self.config.enable_bidirectional_distillation:
                    self.logger.info("初始化双向知识蒸馏框架...")
                    self.distillation_framework = create_bidirectional_distillation_framework(
                        self.edge_model, self.cloud_model,
                        temperature=4.0,
                        alpha=0.7,
                        reverse_weight=0.3,
                        mutual_info_weight=0.2,
                        contrastive_weight=0.1
                    )
                else:
                    self.logger.info("初始化传统知识蒸馏框架...")
                    self.distillation_framework = create_distillation_framework(
                        self.edge_model, self.cloud_model, enable_bidirectional=False
                    )
            
            self.is_initialized = True
            self.logger.info("端云协同推荐系统初始化完成")
            
        except Exception as e:
            self.logger.error(f"系统初始化失败: {e}")
            raise
    
    def _create_model_args(self):
        """创建模型参数对象"""
        class Args:
            def __init__(self, config: SystemConfig):
                self.device = torch.device(config.device)
                self.rec_pre_trained_data = config.rec_pre_trained_data
                self.llm = config.llm_model
                self.recsys = config.recsys_model
                self.maxlen = config.maxlen
                # 添加缺失的属性
                self.nn_parameter = False
                self.token = False
                self.save_dir = 'model_train_multibest'  # 使用训练好的模型目录
                self.batch_size = 20
                self.batch_size_infer = 20
                self.stage2_lr = 0.0001

        return Args(self.config)
    
    async def recommend(
        self, 
        user_sequence: List[int],
        user_id: str,
        num_recommendations: Optional[int] = None,
        mode: Optional[RecommendationMode] = None
    ) -> Dict[str, Any]:
        """
        生成推荐
        
        Args:
            user_sequence: 用户交互序列
            user_id: 用户ID
            num_recommendations: 推荐数量
            mode: 推荐模式
            
        Returns:
            推荐结果
        """
        if not self.is_initialized:
            await self.initialize()
        
        start_time = time.time()
        num_recommendations = num_recommendations or self.config.default_num_recommendations
        mode = mode or self.config.default_recommendation_mode
        
        try:
            # 根据模式选择推荐策略
            if mode == RecommendationMode.EDGE_ONLY:
                result = await self._edge_recommend(user_sequence, user_id, num_recommendations)
            elif mode == RecommendationMode.CLOUD_ONLY:
                result = await self._cloud_recommend(user_sequence, user_id, num_recommendations)
            elif mode == RecommendationMode.COLLABORATIVE:
                result = await self._collaborative_recommend(user_sequence, user_id, num_recommendations)
            elif mode == RecommendationMode.ADAPTIVE:
                result = await self._adaptive_recommend(user_sequence, user_id, num_recommendations)
            else:
                raise ValueError(f"不支持的推荐模式: {mode}")
            
            # 更新性能指标
            response_time = time.time() - start_time
            self._update_metrics(mode, response_time, True)
            
            # 添加到蒸馏队列
            if self.config.enable_knowledge_distillation:
                self.distillation_queue.append((user_sequence, user_id))
                await self._check_distillation_trigger()
            
            result['response_time'] = response_time
            result['recommendation_mode'] = mode.value
            result['timestamp'] = time.time()
            
            return result
            
        except Exception as e:
            # 更新失败指标
            response_time = time.time() - start_time
            self._update_metrics(mode, response_time, False)
            
            self.logger.error(f"推荐生成失败: {e}")
            
            # 返回错误结果
            return {
                'success': False,
                'error': str(e),
                'user_id': user_id,
                'response_time': response_time,
                'recommendation_mode': mode.value,
                'timestamp': time.time()
            }
    
    async def _edge_recommend(
        self, 
        user_sequence: List[int], 
        user_id: str, 
        num_recommendations: int
    ) -> Dict[str, Any]:
        """端侧推荐"""
        recommendations = self.edge_model.local_recommend(
            user_sequence, num_recommendations
        )
        
        # 获取推荐物品信息
        recommendation_details = []
        for item_id in recommendations:
            item_info = self.edge_model.get_item_info(item_id)
            recommendation_details.append(item_info)
        
        return {
            'success': True,
            'user_id': user_id,
            'recommendations': recommendation_details,
            'num_recommendations': len(recommendations),
            'confidence_score': 0.8,  # 端侧推荐的默认置信度
            'source': 'edge'
        }
    
    async def _cloud_recommend(
        self, 
        user_sequence: List[int], 
        user_id: str, 
        num_recommendations: int
    ) -> Dict[str, Any]:
        """云端推荐"""
        # 生成用户表示
        user_repr = self.edge_model.generate_user_representation(user_sequence, user_id)
        
        # 发送到云端
        cloud_result = self.cloud_model.generate_recommendations(
            user_repr, user_id, num_recommendations
        )
        
        return {
            'success': True,
            'user_id': user_id,
            'recommendations': cloud_result['recommendations'],
            'num_recommendations': cloud_result['num_recommendations'],
            'confidence_score': 0.9,  # 云端推荐的默认置信度
            'source': 'cloud',
            'generated_text': cloud_result.get('generated_text', '')
        }
    
    async def _collaborative_recommend(
        self, 
        user_sequence: List[int], 
        user_id: str, 
        num_recommendations: int
    ) -> Dict[str, Any]:
        """端云协同推荐"""
        # 并行执行端侧和云端推荐
        edge_task = self._edge_recommend(user_sequence, user_id, num_recommendations)
        cloud_task = self._cloud_recommend(user_sequence, user_id, num_recommendations)
        
        try:
            edge_result, cloud_result = await asyncio.gather(edge_task, cloud_task)
            
            # 融合推荐结果
            fused_recommendations = self._fuse_recommendations(
                edge_result['recommendations'],
                cloud_result['recommendations'],
                num_recommendations
            )
            
            return {
                'success': True,
                'user_id': user_id,
                'recommendations': fused_recommendations,
                'num_recommendations': len(fused_recommendations),
                'confidence_score': 0.95,  # 协同推荐的高置信度
                'source': 'collaborative',
                'edge_count': len(edge_result['recommendations']),
                'cloud_count': len(cloud_result['recommendations'])
            }
            
        except Exception as e:
            # 如果云端失败，降级到端侧推荐
            self.logger.warning(f"协同推荐失败，降级到端侧推荐: {e}")
            return await self._edge_recommend(user_sequence, user_id, num_recommendations)
    
    async def _adaptive_recommend(
        self, 
        user_sequence: List[int], 
        user_id: str, 
        num_recommendations: int
    ) -> Dict[str, Any]:
        """自适应推荐"""
        # 根据网络状况和系统负载选择推荐模式
        network_quality = await self._assess_network_quality()
        system_load = self._assess_system_load()
        
        if network_quality > 0.8 and system_load < 0.6:
            # 网络良好，系统负载低：使用协同推荐
            return await self._collaborative_recommend(user_sequence, user_id, num_recommendations)
        elif network_quality > 0.5:
            # 网络一般：使用云端推荐
            return await self._cloud_recommend(user_sequence, user_id, num_recommendations)
        else:
            # 网络较差：使用端侧推荐
            return await self._edge_recommend(user_sequence, user_id, num_recommendations)
    
    def _fuse_recommendations(
        self, 
        edge_recs: List[Dict[str, Any]], 
        cloud_recs: List[Dict[str, Any]], 
        num_recommendations: int
    ) -> List[Dict[str, Any]]:
        """融合端云推荐结果"""
        # 简单的交替融合策略
        fused = []
        edge_idx = cloud_idx = 0
        
        for i in range(num_recommendations):
            if i % 2 == 0 and edge_idx < len(edge_recs):
                # 偶数位置优先选择端侧推荐
                fused.append(edge_recs[edge_idx])
                edge_idx += 1
            elif cloud_idx < len(cloud_recs):
                # 奇数位置或端侧推荐用完时选择云端推荐
                fused.append(cloud_recs[cloud_idx])
                cloud_idx += 1
            elif edge_idx < len(edge_recs):
                # 云端推荐用完时继续使用端侧推荐
                fused.append(edge_recs[edge_idx])
                edge_idx += 1
            else:
                # 两边都用完了
                break
        
        return fused
    
    async def _assess_network_quality(self) -> float:
        """评估网络质量"""
        if not self.communicator:
            return 0.0
        
        # 基于通信器的性能统计评估网络质量
        stats = self.communicator.get_performance_statistics()
        success_rate = stats.get('success_rate', 0.0)
        avg_response_time = stats.get('average_response_time', float('inf'))
        
        # 简单的网络质量评分
        quality_score = success_rate * 0.7
        if avg_response_time < 1.0:
            quality_score += 0.3
        elif avg_response_time < 3.0:
            quality_score += 0.2
        elif avg_response_time < 5.0:
            quality_score += 0.1
        
        return min(quality_score, 1.0)
    
    def _assess_system_load(self) -> float:
        """评估系统负载"""
        # 简化的系统负载评估
        current_time = time.time()
        recent_requests = sum(
            1 for t in [self.metrics.last_update_time] 
            if current_time - t < 60  # 最近1分钟的请求
        )
        
        # 基于请求频率评估负载
        load_score = min(recent_requests / 100.0, 1.0)
        return load_score

    async def _check_distillation_trigger(self):
        """检查是否触发知识蒸馏"""
        if (len(self.distillation_queue) >= self.config.distillation_interval and
            self.distillation_framework):

            distillation_type = "双向" if self.config.enable_bidirectional_distillation else "单向"
            self.logger.info(f"触发{distillation_type}知识蒸馏...")

            # 提取用户序列和ID
            user_sequences = [item[0] for item in self.distillation_queue]
            user_ids = [item[1] for item in self.distillation_queue]

            # 收集教师知识
            knowledge_count = self.distillation_framework.collect_teacher_knowledge(
                user_sequences, user_ids
            )

            if knowledge_count > 0:
                # 执行知识蒸馏
                distillation_metrics = self.distillation_framework.distill_knowledge(num_epochs=5)
                self.metrics.knowledge_distillation_count += 1

                # 获取蒸馏统计信息
                distillation_stats = self.distillation_framework.get_distillation_statistics()

                self.logger.info(f"{distillation_type}知识蒸馏完成:")
                self.logger.info(f"  收集知识数量: {knowledge_count}")
                self.logger.info(f"  最新准确率: {distillation_stats.get('latest_accuracy', 0):.4f}")
                if self.config.enable_bidirectional_distillation:
                    self.logger.info(f"  蒸馏模式: {distillation_stats.get('distillation_mode', 'unknown')}")

            # 清空蒸馏队列
            self.distillation_queue.clear()

    def _update_metrics(self, mode: RecommendationMode, response_time: float, success: bool):
        """更新性能指标"""
        self.metrics.total_recommendations += 1

        if success:
            # 更新模式计数
            if mode == RecommendationMode.EDGE_ONLY:
                self.metrics.edge_recommendations += 1
                self.metrics.edge_response_time = self._update_average(
                    self.metrics.edge_response_time, response_time, self.metrics.edge_recommendations
                )
            elif mode == RecommendationMode.CLOUD_ONLY:
                self.metrics.cloud_recommendations += 1
                self.metrics.cloud_response_time = self._update_average(
                    self.metrics.cloud_response_time, response_time, self.metrics.cloud_recommendations
                )
            elif mode == RecommendationMode.COLLABORATIVE:
                self.metrics.collaborative_recommendations += 1

        # 更新总体响应时间
        self.metrics.average_response_time = self._update_average(
            self.metrics.average_response_time, response_time, self.metrics.total_recommendations
        )

        # 更新系统运行时间
        self.metrics.system_uptime = time.time() - self.start_time
        self.metrics.last_update_time = time.time()

    def _update_average(self, current_avg: float, new_value: float, count: int) -> float:
        """更新平均值"""
        if count <= 1:
            return new_value
        return (current_avg * (count - 1) + new_value) / count

    def get_system_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        stats = asdict(self.metrics)

        # 添加组件统计
        if self.edge_model:
            stats['edge_privacy_stats'] = self.edge_model.get_privacy_statistics()

        if self.cloud_model:
            stats['cloud_inference_stats'] = self.cloud_model.get_inference_statistics()

        if self.communicator:
            stats['communication_stats'] = self.communicator.get_performance_statistics()

        if self.distillation_framework:
            stats['distillation_stats'] = self.distillation_framework.get_distillation_statistics()

        return stats

    async def batch_recommend(
        self,
        batch_data: List[Tuple[List[int], str]],
        num_recommendations: int = 10,
        mode: Optional[RecommendationMode] = None
    ) -> List[Dict[str, Any]]:
        """
        批量推荐

        Args:
            batch_data: [(用户序列, 用户ID), ...]
            num_recommendations: 推荐数量
            mode: 推荐模式

        Returns:
            批量推荐结果
        """
        tasks = []
        for user_sequence, user_id in batch_data:
            task = self.recommend(user_sequence, user_id, num_recommendations, mode)
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    'success': False,
                    'error': str(result),
                    'user_id': batch_data[i][1],
                    'timestamp': time.time()
                })
            else:
                processed_results.append(result)

        return processed_results

    async def update_models(self, edge_model_path: Optional[str] = None, cloud_model_path: Optional[str] = None):
        """
        更新模型

        Args:
            edge_model_path: 端侧模型路径
            cloud_model_path: 云端模型路径
        """
        if edge_model_path and self.edge_model:
            self.logger.info(f"更新端侧模型: {edge_model_path}")
            self.edge_model.load_model(edge_model_path)

        if cloud_model_path and self.cloud_model:
            self.logger.info(f"更新云端模型: {cloud_model_path}")
            self.cloud_model.load_model(cloud_model_path)

    def save_system_state(self, save_dir: str):
        """
        保存系统状态

        Args:
            save_dir: 保存目录
        """
        save_path = Path(save_dir)
        save_path.mkdir(parents=True, exist_ok=True)

        # 保存配置
        config_path = save_path / "system_config.json"
        with open(config_path, 'w') as f:
            # 将枚举转换为字符串
            config_dict = asdict(self.config)
            config_dict['default_recommendation_mode'] = self.config.default_recommendation_mode.value
            json.dump(config_dict, f, indent=2)

        # 保存指标
        metrics_path = save_path / "system_metrics.json"
        with open(metrics_path, 'w') as f:
            json.dump(asdict(self.metrics), f, indent=2)

        # 保存模型
        if self.edge_model:
            self.edge_model.save_model(str(save_path / "edge_model.pt"))

        if self.cloud_model:
            self.cloud_model.save_model(str(save_path / "cloud_model.pt"))

        if self.distillation_framework:
            self.distillation_framework.save_framework(str(save_path / "distillation_framework.pt"))

        self.logger.info(f"系统状态已保存到 {save_dir}")

    async def close(self):
        """关闭系统"""
        self.logger.info("正在关闭端云协同推荐系统...")

        if self.communicator:
            await self.communicator.close()

        self.is_initialized = False
        self.logger.info("端云协同推荐系统已关闭")


async def create_edge_cloud_system(
    config: Optional[SystemConfig] = None,
    auto_initialize: bool = True
) -> EdgeCloudRecommendationSystem:
    """
    创建端云协同推荐系统的便捷函数

    Args:
        config: 系统配置
        auto_initialize: 是否自动初始化

    Returns:
        端云协同推荐系统实例
    """
    system = EdgeCloudRecommendationSystem(config)

    if auto_initialize:
        await system.initialize()

    return system


# 示例使用
async def main():
    """示例主函数"""
    # 创建系统配置
    config = SystemConfig(
        device="cuda" if torch.cuda.is_available() else "cpu",
        rec_pre_trained_data="Movies_and_TV",
        default_recommendation_mode=RecommendationMode.COLLABORATIVE,
        privacy_level="high",
        enable_knowledge_distillation=True
    )

    # 创建并初始化系统
    system = await create_edge_cloud_system(config)

    try:
        # 示例推荐请求
        user_sequence = [1, 5, 23, 45, 67, 89, 123]
        user_id = "user_001"

        # 生成推荐
        result = await system.recommend(
            user_sequence=user_sequence,
            user_id=user_id,
            num_recommendations=10,
            mode=RecommendationMode.COLLABORATIVE
        )

        print("推荐结果:", result)

        # 获取系统统计
        stats = system.get_system_statistics()
        print("系统统计:", stats)

    finally:
        # 关闭系统
        await system.close()


if __name__ == "__main__":
    asyncio.run(main())
