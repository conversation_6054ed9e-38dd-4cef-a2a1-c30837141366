#!/usr/bin/env python3
"""
Novel推荐系统评估脚本

支持评估不同类型的模型：
- cf_srec: CF-SRec端侧模型
- collaborative: 端云协同系统

使用方法:
    python evaluate.py --model_type cf_srec --dataset Movies_and_TV --model_path checkpoints/cf_srec_Movies_and_TV/best_model.pth
"""

import argparse
import os
import sys
import logging
import torch
import numpy as np
from pathlib import Path
from typing import Dict, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from models.client.cf_srec import CFSRec
from datasets import DatasetManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Novel推荐系统评估')
    
    # 模型配置
    parser.add_argument('--model_type', type=str, required=True,
                       choices=['cf_srec', 'collaborative'],
                       help='模型类型')
    parser.add_argument('--model_path', type=str, required=True,
                       help='模型文件路径')
    parser.add_argument('--cf_srec_path', type=str, default=None,
                       help='CF-SRec模型路径（协同模式需要）')
    parser.add_argument('--llm_path', type=str, default=None,
                       help='LLM模型路径（协同模式需要）')
    
    # 数据集配置
    parser.add_argument('--dataset', type=str, required=True,
                       help='数据集名称')
    parser.add_argument('--eval_users', type=int, default=2000,
                       help='评估用户数量')
    
    # 评估配置
    parser.add_argument('--batch_size', type=int, default=64,
                       help='评估批次大小')
    parser.add_argument('--top_k_list', type=int, nargs='+', default=[5, 10, 20],
                       help='Top-K评估列表')
    parser.add_argument('--device', type=str, default='cuda:0',
                       help='评估设备')
    
    # 输出配置
    parser.add_argument('--output_dir', type=str, default='evaluation_results',
                       help='结果输出目录')
    parser.add_argument('--save_predictions', action='store_true',
                       help='保存预测结果')
    
    return parser.parse_args()


def load_cf_srec_model(model_path: str, dataset_info) -> CFSRec:
    """加载CF-SRec模型"""
    # 获取物品数量，处理不同的数据结构
    if hasattr(dataset_info, 'item_num'):
        item_num = dataset_info.item_num
    elif isinstance(dataset_info, dict) and 'item_num' in dataset_info:
        item_num = dataset_info['item_num']
    else:
        item_num = 10000  # 默认值

    config = {
        'item_num': item_num,
        'hidden_size': 64,
        'max_seq_length': 50,
        'num_attention_heads': 2,
        'num_hidden_layers': 2,
        'dropout_prob': 0.1,
        'user_repr_dim': 64
    }
    
    model = CFSRec(config)
    
    if os.path.exists(model_path):
        state_dict = torch.load(model_path, map_location='cpu')
        model.load_state_dict(state_dict)
        logger.info(f"已加载CF-SRec模型: {model_path}")
    else:
        logger.warning(f"模型文件不存在: {model_path}，使用随机初始化的模型")
    
    return model


def evaluate_cf_srec(model: CFSRec, test_data: List, args) -> Dict:
    """评估CF-SRec模型"""
    model.eval()
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    model.to(device)
    
    all_predictions = []
    all_targets = []
    
    logger.info(f"开始评估CF-SRec模型，测试用户数: {len(test_data)}")
    
    with torch.no_grad():
        for i, (user_history, target_items) in enumerate(test_data):
            if i >= args.eval_users:
                break
            
            # 生成推荐
            recommendations = model.recommend(user_history, top_k=max(args.top_k_list))
            
            all_predictions.append(recommendations)
            all_targets.append(target_items)
            
            if (i + 1) % 100 == 0:
                logger.info(f"已评估 {i + 1} 个用户")
    
    # 计算指标
    metrics = {}
    for k in args.top_k_list:
        ndcg_k = calculate_ndcg_at_k(all_predictions, all_targets, k)
        hit_rate_k = calculate_hit_rate_at_k(all_predictions, all_targets, k)
        recall_k = calculate_recall_at_k(all_predictions, all_targets, k)
        
        metrics[f'ndcg@{k}'] = ndcg_k
        metrics[f'hit_rate@{k}'] = hit_rate_k
        metrics[f'recall@{k}'] = recall_k
    
    return metrics


def calculate_ndcg_at_k(predictions: List[List[int]], targets: List[List[int]], k: int) -> float:
    """计算NDCG@K"""
    ndcg_scores = []
    
    for pred, target in zip(predictions, targets):
        pred_k = pred[:k]
        
        # 计算DCG
        dcg = 0.0
        for i, item in enumerate(pred_k):
            if item in target:
                dcg += 1.0 / np.log2(i + 2)
        
        # 计算IDCG
        idcg = 0.0
        for i in range(min(len(target), k)):
            idcg += 1.0 / np.log2(i + 2)
        
        # 计算NDCG
        if idcg > 0:
            ndcg_scores.append(dcg / idcg)
        else:
            ndcg_scores.append(0.0)
    
    return np.mean(ndcg_scores)


def calculate_hit_rate_at_k(predictions: List[List[int]], targets: List[List[int]], k: int) -> float:
    """计算Hit Rate@K"""
    hits = 0
    total = len(predictions)
    
    for pred, target in zip(predictions, targets):
        pred_k = pred[:k]
        if any(item in target for item in pred_k):
            hits += 1
    
    return hits / total if total > 0 else 0.0


def calculate_recall_at_k(predictions: List[List[int]], targets: List[List[int]], k: int) -> float:
    """计算Recall@K"""
    recall_scores = []
    
    for pred, target in zip(predictions, targets):
        pred_k = pred[:k]
        
        if len(target) == 0:
            recall_scores.append(0.0)
        else:
            hits = sum(1 for item in pred_k if item in target)
            recall_scores.append(hits / len(target))
    
    return np.mean(recall_scores)


def create_mock_test_data(dataset_info, num_users: int = 2000) -> List:
    """创建模拟测试数据"""
    test_data = []

    # 获取物品数量，处理不同的数据结构
    if hasattr(dataset_info, 'item_num'):
        item_num = dataset_info.item_num
    elif isinstance(dataset_info, dict) and 'item_num' in dataset_info:
        item_num = dataset_info['item_num']
    else:
        item_num = 10000  # 默认值

    for i in range(num_users):
        # 生成随机用户历史
        history_length = np.random.randint(5, 20)
        user_history = np.random.randint(1, item_num, history_length).tolist()

        # 生成目标物品
        target_items = np.random.randint(1, item_num, 3).tolist()

        test_data.append((user_history, target_items))

    return test_data


def main():
    """主函数"""
    args = parse_args()
    
    logger.info(f"开始评估: {args.model_type}")
    logger.info(f"数据集: {args.dataset}")
    logger.info(f"模型路径: {args.model_path}")
    
    # 准备数据集
    dataset_manager = DatasetManager()
    dataset_info = dataset_manager.get_dataset_info(args.dataset)
    
    if dataset_info is None:
        logger.error(f"数据集 {args.dataset} 不存在")
        return
    
    # 创建测试数据（这里使用模拟数据，实际应该加载真实测试集）
    test_data = create_mock_test_data(dataset_info, args.eval_users)
    logger.info(f"创建了 {len(test_data)} 个测试样本")
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        if args.model_type == 'cf_srec':
            # 评估CF-SRec模型
            model = load_cf_srec_model(args.model_path, dataset_info)
            metrics = evaluate_cf_srec(model, test_data, args)
            
        elif args.model_type == 'collaborative':
            # 评估协同系统
            logger.info("协同系统评估功能正在开发中...")
            metrics = {'status': 'not_implemented'}
            
        else:
            raise ValueError(f"未知的模型类型: {args.model_type}")
        
        # 输出结果
        logger.info("评估结果:")
        for metric, value in metrics.items():
            if isinstance(value, float):
                logger.info(f"  {metric}: {value:.4f}")
            else:
                logger.info(f"  {metric}: {value}")
        
        # 保存结果
        import json
        results_file = output_dir / f"evaluation_results_{args.model_type}_{args.dataset}.json"
        with open(results_file, 'w') as f:
            json.dump(metrics, f, indent=2)
        
        logger.info(f"结果已保存到: {results_file}")
        
    except Exception as e:
        logger.error(f"评估失败: {e}")
        raise


if __name__ == '__main__':
    main()
