"""
LLM-SRec 主模型（models/seqllm_model.py）

作用：
- 将预训练的序列推荐模型（RecSys/SASRec）与大语言模型（llm4rec）组合
- 负责数据准备、文案构造、向量替换与提取、训练/评测和推理
- 暴露训练阶段（pre_train_phase2）、离线生成（generate_batch）与向量提取（extract_emb）等接口

重要概念：
- RecSys：加载并冻结的序列推荐模型（SASRec），提供历史行为序列的隐藏表示
- llm4rec：基于LLM的表征与匹配头，学习将文本与历史嵌入映射到统一向量空间
- [UserOut]/[ItemOut]/[HistoryEmb]：在prompt中占位的"特殊token"，通过替换机制注入向量

文件结构概览：
- llmrec_model：主模型类
  - save_model / load_model：保存与加载训练好的部件
  - make_interact_text / make_candidate_text：将用户历史与候选物品转为可读prompt
  - pre_train_phase2：核心训练循环（构造样本 → LLM前向 → 计算损失 → 反向传播）
  - generate_batch：推理阶段按批量生成物品表示并完成排序评估
  - extract_emb：仅提取用户表示
"""

import random  # 用于负样本随机采样和数据打乱
import pickle  # 用于加载物品文本信息字典

import torch
from torch.cuda.amp import autocast as autocast  # 混合精度训练加速
import torch.nn as nn
import numpy as np

from models.recsys_model import *      # 导入RecSys封装类
from models.seqllm4rec import *        # 导入llm4rec核心模块
from sentence_transformers import SentenceTransformer  # 句子嵌入模型（当前未使用）
from datetime import datetime          # 用于时间戳格式化

from tqdm import trange, tqdm         # 进度条显示

# 可选的Habana硬件加速支持
try:
    import habana_frameworks.torch.core as htcore
except:
    pass  # 如果没有Habana硬件，忽略导入错误


class llmrec_model(nn.Module):
    """
    LLM-SRec 主模型：组合 RecSys（SASRec）与 llm4rec（LLM）。

    - 读取预训练的CF-SRec（SASRec）并冻结，用作教师模型/特征抽取器
    - 初始化llm4rec（加载LLM与预测头），并配置嵌入投影层 item_emb_proj
    - 在训练/评测/推理流程中，构建Prompt、替换特殊token、抽取向量并计算损失
    """
    def __init__(self, args):
        """
        初始化 LLM-SRec 主模型

        Args:
            args: 全局配置对象，包含以下关键参数：
                - rec_pre_trained_data: 预训练数据集名称
                - recsys: 推荐系统模型名称（如'sasrec'）
                - llm: 大语言模型名称（如'llama'）
                - device: 计算设备
                - maxlen: 序列最大长度
                - nn_parameter: 是否使用神经网络处理器优化
        """
        super().__init__()
        rec_pre_trained_data = args.rec_pre_trained_data
        self.args = args
        self.device = args.device

        # 加载物品文本与时间信息的映射字典
        # 包含：title（标题）、description（描述）、time（时间戳）等信息
        with open(f'./SeqRec/data_{args.rec_pre_trained_data}/text_name_dict.json.gz','rb') as ft:
            self.text_name_dict = pickle.load(ft)

        # 初始化并加载预训练的CF-SRec模型（SASRec）
        # 该模型参数已冻结，用作固定的特征提取器
        self.recsys = RecSys(args.recsys, rec_pre_trained_data, self.device)

        # 保存关键模型信息
        self.item_num = self.recsys.item_num           # 物品总数
        self.rec_sys_dim = self.recsys.hidden_units    # CF-SRec的嵌入维度
        self.sbert_dim = 768                           # 句子嵌入维度（保留，当前未使用）

        # 初始化损失函数
        self.mse = nn.MSELoss()    # 均方误差损失，用于表示对齐
        self.l1 = nn.L1Loss()     # L1损失（保留，当前未使用）

        # 缓存变量
        self.all_embs = None       # 缓存所有物品的LLM表示（推理时使用）
        self.maxlen = args.maxlen  # 序列最大长度

        # 评估指标累积器
        self.NDCG = 0      # NDCG@10 累积值
        self.HIT = 0       # Hit@10 累积值（别名为HT）
        self.NDCG_20 = 0   # NDCG@20 累积值
        self.HIT_20 = 0    # Hit@20 累积值

        # 额外的评估指标（保留用于扩展）
        self.rec_NDCG = 0  # 推荐系统NDCG（当前未使用）
        self.rec_HIT = 0   # 推荐系统Hit Rate（当前未使用）
        self.lan_NDCG = 0  # 语言模型NDCG（当前未使用）
        self.lan_HIT = 0   # 语言模型Hit Rate（当前未使用）
        self.num_user = 0  # 用户数量统计（当前未使用）
        self.yes = 0       # 通用计数器（当前未使用）

        # 用户表示提取结果存储列表
        self.extract_embs_list = []

        # 二元交叉熵损失函数（保留用于扩展，当前未使用）
        self.bce_criterion = torch.nn.BCEWithLogitsLoss()

        # 初始化LLM端模块（llm4rec）
        # 包含：LLM模型、特殊token处理、预测头等
        self.llm = llm4rec(device=self.device, llm_model=args.llm, args=self.args)

        # 物品嵌入投影层：将CF-SRec的物品嵌入映射到LLM隐层维度
        # 这是连接CF-SRec和LLM的关键桥梁
        # 架构：Linear -> LayerNorm -> LeakyReLU -> Linear
        self.item_emb_proj = nn.Sequential(
            nn.Linear(self.rec_sys_dim, self.llm.llm_model.config.hidden_size),  # 维度映射
            nn.LayerNorm(self.llm.llm_model.config.hidden_size),                 # 层归一化
            nn.LeakyReLU(),                                                       # 激活函数
            nn.Linear(self.llm.llm_model.config.hidden_size, self.llm.llm_model.config.hidden_size)  # 进一步变换
        )

        # 使用Xavier正态分布初始化线性层权重
        # 有助于训练稳定性和收敛速度
        nn.init.xavier_normal_(self.item_emb_proj[0].weight)
        nn.init.xavier_normal_(self.item_emb_proj[3].weight)

        # 评估统计变量（与前面的变量重复，保持兼容性）
        self.users = 0.0   # 评估用户数量
        self.NDCG = 0.0    # NDCG累积值
        self.HT = 0.0      # Hit Rate累积值（HT是HIT的别名）



    def save_model(self, args, epoch2=None, best=False):
        """
        保存当前训练好的模型组件到磁盘

        保存策略：
        - 仅保存可训练的组件（CF-SRec保持冻结，无需保存）
        - 根据训练模式保存不同的组件组合
        - 支持保存最佳模型到单独目录

        Args:
            args: 全局配置对象
            epoch2 (int, optional): 当前训练轮次，用于文件命名
            best (bool): 是否保存为最佳模型，默认False

        文件命名格式：
            ./models/{save_dir}/[best/]{rec_pre_trained_data}_{llm}_{epoch2}_{component}.pt
        """
        # 构建保存目录路径
        out_dir = f'./models/{args.save_dir}/'
        if best:
            out_dir = out_dir[:-1] + 'best/'  # 最佳模型保存到best子目录

        # 确保目录存在
        create_dir(out_dir)

        # 构建文件名前缀：数据集_模型_轮次_
        out_dir += f'{args.rec_pre_trained_data}_'
        out_dir += f'{args.llm}_{epoch2}_'

        # 仅在训练模式下保存模型
        if args.train:
            # 1. 保存物品嵌入投影层（连接CF-SRec和LLM的桥梁）
            torch.save(self.item_emb_proj.state_dict(), out_dir + 'item_proj.pt')

            # 2. 保存用户预测头（将LLM隐藏状态映射到用户表示空间）
            torch.save(self.llm.pred_user.state_dict(), out_dir + 'pred_user.pt')

            # 3. 保存物品预测头（将LLM隐藏状态映射到物品表示空间）
            torch.save(self.llm.pred_item.state_dict(), out_dir + 'pred_item.pt')

            # 4. 根据训练策略保存不同的特殊token表示
            if not args.token:
                # 策略A：使用可学习的CLS向量替换[UserOut]/[ItemOut]
                if args.nn_parameter:
                    # 神经网络处理器模式：直接保存Parameter的data
                    torch.save(self.llm.CLS.data, out_dir + 'CLS.pt')
                    torch.save(self.llm.CLS_item.data, out_dir + 'CLS_item.pt')
                else:
                    # 标准模式：保存Embedding层的state_dict
                    torch.save(self.llm.CLS.state_dict(), out_dir + 'CLS.pt')
                    torch.save(self.llm.CLS_item.state_dict(), out_dir + 'CLS_item.pt')
            if args.token:
                # 策略B：训练LLM的词嵌入层
                torch.save(self.llm.llm_model.model.embed_tokens.state_dict(), out_dir + 'token.pt')


    def load_model(self, args, phase1_epoch=None, phase2_epoch=None):
        """
        从磁盘加载预训练的模型组件

        加载策略：
        - 按照save_model的保存格式加载对应组件
        - 根据训练策略加载不同的特殊token表示
        - 使用map_location确保设备兼容性

        Args:
            args: 全局配置对象
            phase1_epoch (int, optional): 第一阶段轮次（当前未使用，保留兼容性）
            phase2_epoch (int, optional): 第二阶段轮次，用于确定加载的模型版本

        注意：
            - 必须与save_model的保存格式完全匹配
            - 加载后显式删除临时变量释放内存
        """
        # 构建加载路径，与save_model的命名格式一致
        out_dir = f'./models/{args.save_dir}/{args.rec_pre_trained_data}_'
        out_dir += f'{args.llm}_{phase2_epoch}_'

        # 1. 加载物品嵌入投影层
        item_emb_proj = torch.load(out_dir + 'item_proj.pt', map_location=self.device)
        self.item_emb_proj.load_state_dict(item_emb_proj)
        del item_emb_proj  # 释放内存

        # 2. 加载用户预测头
        pred_user = torch.load(out_dir + 'pred_user.pt', map_location=self.device)
        self.llm.pred_user.load_state_dict(pred_user)
        del pred_user  # 释放内存

        # 3. 加载物品预测头
        pred_item = torch.load(out_dir + 'pred_item.pt', map_location=self.device)
        self.llm.pred_item.load_state_dict(pred_item)
        del pred_item  # 释放内存

        # 4. 根据训练策略加载特殊token表示
        if not args.token:
            # 策略A：加载可学习的CLS向量
            CLS = torch.load(out_dir + 'CLS.pt', map_location=self.device)
            self.llm.CLS.load_state_dict(CLS)
            del CLS  # 释放内存

            CLS_item = torch.load(out_dir + 'CLS_item.pt', map_location=self.device)
            self.llm.CLS_item.load_state_dict(CLS_item)
            del CLS_item  # 释放内存

        if args.token:
            # 策略B：加载训练好的词嵌入层
            token = torch.load(out_dir + 'token.pt', map_location=self.device)
            self.llm.llm_model.model.embed_tokens.load_state_dict(token)
            del token  # 释放内存
            

    def find_item_text(self, item, title_flag=True, description_flag=True):
        """
        批量获取物品的文本信息（标题和/或描述）

        Args:
            item (list): 物品ID列表
            title_flag (bool): 是否包含标题，默认True
            description_flag (bool): 是否包含描述，默认True

        Returns:
            list: 格式化的物品文本列表，每个元素为带引号的字符串
        """
        t = 'title'
        d = 'description'
        t_ = 'No Title'      # 缺失标题的默认值
        d_ = 'No Description'  # 缺失描述的默认值

        if title_flag and description_flag:
            # 返回 "标题, 描述" 格式
            return [f'"{self.text_name_dict[t].get(i,t_)}, {self.text_name_dict[d].get(i,d_)}"' for i in item]
        elif title_flag and not description_flag:
            # 仅返回标题
            return [f'"{self.text_name_dict[t].get(i,t_)}"' for i in item]
        elif not title_flag and description_flag:
            # 仅返回描述
            return [f'"{self.text_name_dict[d].get(i,d_)}"' for i in item]

    def find_item_time(self, item, user, title_flag=True, description_flag=True):
        """
        获取用户与物品交互的时间信息

        Args:
            item (list): 物品ID列表
            user (int): 用户ID
            title_flag (bool): 未使用，保留兼容性
            description_flag (bool): 未使用，保留兼容性

        Returns:
            list: 格式化的日期字符串列表，格式为 'YYYY-MM-DD'
        """
        # 从时间戳字典中获取交互时间（毫秒级时间戳）
        # 转换为UTC时间对象，然后格式化为日期字符串
        # 确保user是Python整数而不是tensor
        user_id = user.item() if hasattr(user, 'item') else user
        l = []
        for i in item:
            try:
                # 尝试获取时间戳
                timestamp = int(self.text_name_dict['time'][i][user_id])/1000
                l.append(datetime.utcfromtimestamp(timestamp))
            except (KeyError, ValueError):
                # 如果没有时间信息，使用默认时间（2020-01-01）
                l.append(datetime(2020, 1, 1))
        return [l_.strftime('%Y-%m-%d') for l_ in l]

    def find_item_text_single(self, item, title_flag=True, description_flag=True):
        """
        获取单个物品的文本信息（标题和/或描述）

        Args:
            item (int): 单个物品ID
            title_flag (bool): 是否包含标题，默认True
            description_flag (bool): 是否包含描述，默认True

        Returns:
            str: 格式化的物品文本字符串，带引号
        """
        t = 'title'
        d = 'description'
        t_ = 'No Title'      # 缺失标题的默认值
        d_ = 'No Description'  # 缺失描述的默认值

        if title_flag and description_flag:
            # 返回 "标题, 描述" 格式
            return f'"{self.text_name_dict[t].get(item,t_)}, {self.text_name_dict[d].get(item,d_)}"'
        elif title_flag and not description_flag:
            # 仅返回标题
            return f'"{self.text_name_dict[t].get(item,t_)}"'
        elif not title_flag and description_flag:
            # 仅返回描述
            return f'"{self.text_name_dict[d].get(item,d_)}"'

    def get_item_emb(self, item_ids):
        """
        从CF-SRec模型获取物品嵌入向量

        Args:
            item_ids (list): 物品ID列表

        Returns:
            torch.Tensor: 物品嵌入张量，形状为 [len(item_ids), embedding_dim]

        注意：
            - 根据 args.nn_parameter 决定使用不同的访问方式
            - nn_parameter=True: 直接索引访问（适用于某些硬件优化）
            - nn_parameter=False: 通过Embedding层调用（标准方式）
        """
        with torch.no_grad():
            if self.args.nn_parameter:
                # 直接通过索引访问嵌入矩阵（硬件优化模式）
                item_embs = self.recsys.model.item_emb[torch.LongTensor(item_ids).to(self.device)]
            else:
                # 通过Embedding层的forward方法（标准模式）
                item_embs = self.recsys.model.item_emb(torch.tensor(item_ids, dtype=torch.long).to(self.device))

        return item_embs
    
    def forward(self, data, optimizer=None, batch_iter=None, mode='phase1'):
        """
        模型前向传播的统一入口，根据模式分发到不同的处理流程

        这是一个路由方法，根据不同的模式调用相应的核心方法：
        - phase2: 训练模式，执行核心训练循环
        - generate_batch: 推理评估模式，生成推荐并计算指标
        - extract: 表示提取模式，仅提取用户表示

        Args:
            data: 输入数据，格式根据模式而定
            optimizer: 优化器实例（仅训练模式需要）
            batch_iter: 训练进度信息（仅训练模式需要）
            mode (str): 运行模式，默认'phase1'
                - 'phase2': 第二阶段训练模式
                - 'generate_batch': 批量推理评估模式
                - 'extract': 用户表示提取模式

        注意：
            - phase1模式当前未实现，保留用于扩展
            - generate_batch模式会自动输出评估结果
        """
        if mode == 'phase2':
            # 训练模式：执行核心训练循环
            self.pre_train_phase2(data, optimizer, batch_iter)

        if mode == 'generate_batch':
            # 推理评估模式：生成推荐并计算评估指标
            self.generate_batch(data)

            # 输出评估结果：按累计用户数抽样打印
            eusers = getattr(self.args, 'eval_log_users', 0)
            if eusers and eusers > 0:
                if (self.users % eusers) < 1e-6:  # 例如每累计 1000 用户打印一次
                    print(self.args.save_dir, self.args.rec_pre_trained_data)
                    print('test (NDCG@10: %.4f, HR@10: %.4f), Num User: %.0f'
                          % (self.NDCG/self.users, self.HT/self.users, self.users))
                    print('test (NDCG@20: %.4f, HR@20: %.4f), Num User: %.0f'
                          % (self.NDCG_20/self.users, self.HIT_20/self.users, self.users))

        if mode == 'extract':
            # 表示提取模式：仅提取用户表示，不进行推荐评估
            self.extract_emb(data)

    def make_interact_text(self, interact_ids, interact_max_num, user):
        """
        构造用户历史交互的文本表示，用于LLM输入

        将用户的历史交互物品转换为可读的文本序列，每个物品包含：
        - 序号标识（Item No.1, Item No.2, ...）
        - 交互时间（格式化为日期）
        - 物品标题文本
        - 特殊占位符 [HistoryEmb] 用于后续的向量替换

        Args:
            interact_ids (list): 用户历史交互的物品ID列表，按时间顺序排列
            interact_max_num (int|str): 最大交互数量限制
                - 整数：取最近的N个交互
                - 'all'：使用全部历史交互
            user (int): 用户ID，用于获取交互时间信息

        Returns:
            Tuple[str, list]:
                - interact_text: 格式化的交互文本，用逗号连接
                  例如: "Item No.1, Time: 2023-01-15, iPhone 13[HistoryEmb],Item No.2, Time: 2023-02-20, MacBook Pro[HistoryEmb]"
                - interact_ids: 实际使用的物品ID列表（可能被截断）

        注意：
            - [HistoryEmb] 是特殊token，会在后续被对应物品的嵌入向量替换
            - 时间信息来自 text_name_dict 中的时间戳数据
        """
        # 获取所有交互物品的标题文本（仅标题，不包含描述）
        interact_item_titles_ = self.find_item_text(interact_ids, title_flag=True, description_flag=False)

        # 获取交互时间信息，用于构造时间上下文
        times = self.find_item_time(interact_ids, user)

        # 存储格式化后的交互文本片段
        interact_text = []
        count = 1  # 物品序号计数器

        # 根据 interact_max_num 决定使用全部历史还是截取最近的交互
        if interact_max_num == 'all':
            # 使用全部历史交互
            times = self.find_item_time(interact_ids, user)
        else:
            # 只使用最近的 interact_max_num 个交互
            # 取列表末尾的元素（最近的交互）
            times = self.find_item_time(interact_ids[-interact_max_num:], user)

        # 构造交互文本序列
        if interact_max_num == 'all':
            # 处理全部历史交互
            for title in interact_item_titles_:
                # 格式：Item No.序号, Time: 日期, 物品标题[HistoryEmb]
                interact_text.append(f'Item No.{count}, Time: {times[count-1]}, ' + title + '[HistoryEmb]')
                count += 1
        else:
            # 处理截取的最近交互
            for title in interact_item_titles_[-interact_max_num:]:
                interact_text.append(f'Item No.{count}, Time: {times[count-1]}, ' + title + '[HistoryEmb]')
                count += 1
            # 同步更新 interact_ids，保持与文本的一致性
            interact_ids = interact_ids[-interact_max_num:]

        # 将所有交互文本片段用逗号连接成完整的历史序列
        interact_text = ','.join(interact_text)
        return interact_text, interact_ids

    
    
    def make_candidate_text(self, interact_ids, candidate_num, target_item_id, target_item_title, candi_set = None, task = 'ItemTask'):
        """
        构造候选物品的文本表示，用于训练阶段的物品表示学习

        为每个候选物品生成包含标题和特殊token的文本prompt，用于：
        1. 正样本：目标物品（用户实际交互的物品）
        2. 负样本：随机采样的未交互物品

        Args:
            interact_ids (list): 用户历史交互的物品ID列表，用于负采样时排除
            candidate_num (int): 候选物品总数（包含1个正样本 + N-1个负样本）
            target_item_id (int): 目标物品ID（正样本）
            target_item_title (str): 目标物品的标题文本
            candi_set (set, optional): 候选物品集合，如果提供则从中采样负样本
            task (str): 任务类型标识，默认为 'ItemTask'

        Returns:
            Tuple[list, list]:
                - candidate_text: 候选物品的文本表示列表
                  格式: "The item title and item embedding are as follows: 物品标题[HistoryEmb], then generate item representation token:[ItemOut]"
                - candidate_ids: 对应的候选物品ID列表（正样本在第一位）

        注意：
            - [HistoryEmb] 会被替换为物品的嵌入向量
            - [ItemOut] 是LLM输出中提取物品表示的位置标记
            - 负采样策略：优先从 candi_set 采样，否则全局随机采样
        """
        neg_item_id = []

        # 负样本采样策略
        if candi_set == None:
            # 策略1: 全局随机采样（当没有提供候选集时）
            neg_item_id = []
            while len(neg_item_id) < 99:  # 最多采样99个负样本
                t = np.random.randint(1, self.item_num + 1)
                # 确保负样本不在用户历史交互中，且不重复
                if not (t in interact_ids or t in neg_item_id):
                    neg_item_id.append(t)
        else:
            # 策略2: 从指定候选集中采样
            his = set(interact_ids)  # 用户历史交互集合
            items = list(candi_set.difference(his))  # 排除历史交互的候选物品

            if len(items) > 99:
                # 候选集足够大，直接随机采样99个
                neg_item_id = random.sample(items, 99)
            else:
                # 候选集不够，补充全局随机采样
                while len(neg_item_id) < 49:  # 减少采样数量避免冲突
                    t = np.random.randint(1, self.item_num + 1)
                    if not (t in interact_ids or t in neg_item_id):
                        neg_item_id.append(t)

        # 打乱负样本顺序，避免位置偏差
        random.shuffle(neg_item_id)

        # 构建候选物品ID列表：正样本在第一位
        candidate_ids = [target_item_id]

        # 构建候选物品文本表示列表
        # 正样本的文本表示（使用提供的标题）
        candidate_text = [f'The item title and item embedding are as follows: ' + target_item_title + "[HistoryEmb], then generate item representation token:[ItemOut]"]

        # 负样本的文本表示（动态获取标题）
        for neg_candidate in neg_item_id[:candidate_num - 1]:
            # 获取负样本物品的标题
            neg_title = self.find_item_text_single(neg_candidate, title_flag=True, description_flag=False)
            candidate_text.append(f'The item title and item embedding are as follows: ' + neg_title + "[HistoryEmb], then generate item representation token:[ItemOut]")
            candidate_ids.append(neg_candidate)

        return candidate_text, candidate_ids
    
    
    def make_candidate(self, interact_ids, candidate_num, target_item_id, target_item_title, candi_set = None, task = 'ItemTask'):
        """
        生成候选物品ID列表，用于推理阶段的推荐评估

        与 make_candidate_text 的区别：
        - make_candidate_text: 生成文本表示 + ID列表（训练用）
        - make_candidate: 仅生成ID列表（推理评估用）

        Args:
            interact_ids (list): 用户历史交互的物品ID列表，用于负采样时排除
            candidate_num (int): 候选物品总数（包含1个正样本 + N-1个负样本）
            target_item_id (int): 目标物品ID（正样本）
            target_item_title (str): 目标物品标题（当前未使用，保留兼容性）
            candi_set (set, optional): 候选物品集合（当前未使用，保留兼容性）
            task (str): 任务类型标识（当前未使用，保留兼容性）

        Returns:
            list: 候选物品ID列表，正样本在第一位，后续为负样本
        """
        neg_item_id = []
        neg_item_id = []  # 重复初始化（可能是代码遗留，但保持不变）

        # 随机采样99个负样本（固定数量，用于评估）
        while len(neg_item_id) < 99:
            t = np.random.randint(1, self.item_num + 1)  # 随机选择物品ID（1到item_num）
            # 确保负样本不在用户历史交互中，且不重复
            if not (t in interact_ids or t in neg_item_id):
                neg_item_id.append(t)

        # 打乱负样本顺序，避免位置偏差
        random.shuffle(neg_item_id)

        # 构建候选物品ID列表：正样本在第一位
        candidate_ids = [target_item_id]

        # 添加指定数量的负样本
        candidate_ids = candidate_ids + neg_item_id[:candidate_num - 1]

        return candidate_ids
    
    
    def pre_train_phase2(self, data, optimizer, batch_iter):
        """
        LLM-SRec 核心训练循环 - 第二阶段预训练

        这是模型的核心训练方法，实现以下关键流程：
        1. 从CF-SRec获取用户历史表示（作为监督信号）
        2. 构造用户历史和候选物品的文本prompt
        3. 通过特殊token机制注入嵌入向量
        4. 计算推荐损失和对齐损失
        5. 反向传播更新模型参数

        Args:
            data (tuple): 批次训练数据 (u, seq, pos, neg)
                - u: 用户ID列表 [batch_size]
                - seq: 用户历史序列 [batch_size, max_len]
                - pos: 正样本物品ID [batch_size, max_len]
                - neg: 负样本物品ID [batch_size, max_len]
            optimizer: PyTorch优化器实例
            batch_iter (tuple): 训练进度信息 (epoch, total_epoch, step, total_step)

        核心思想：
            - 使用冻结的CF-SRec作为"教师"提供用户表示
            - 训练LLM学习从文本+嵌入中生成相同的用户表示
            - 同时学习物品表示用于推荐排序
        """
        # 解包训练进度信息，用于日志输出
        epoch, total_epoch, step, total_step = batch_iter
        if getattr(self.args, 'log_interval', 50) > 0 and (step % self.args.log_interval) == 0:
            print(self.args.save_dir, self.args.rec_pre_trained_data, self.args.llm)

        # 清零梯度，开始新的训练步骤
        optimizer.zero_grad()

        # 解包批次数据
        u, seq, pos, neg = data

        # 保存原始序列的副本（虽然当前未使用，但保留用于调试）
        original_seq = seq.copy()

        # 初始化损失累积变量（当前未使用，但保留用于扩展）
        mean_loss = 0

        # 初始化批次数据容器
        text_input = []           # 用户历史的文本prompt列表
        candidates_pos = []       # 候选物品的文本prompt列表
        candidates_neg = []       # 负样本文本（当前未使用）
        interact_embs = []        # 历史物品的嵌入向量列表
        candidate_embs_pos = []   # 候选物品的嵌入向量列表
        candidate_embs_neg = []   # 负样本嵌入（当前未使用）
        candidate_embs = []       # 最终的候选物品嵌入张量

        # 保留的损失变量（当前未使用，但保留用于多模式训练）
        loss_rm_mode1 = 0
        loss_rm_mode2 = 0

        # 步骤1: 从冻结的CF-SRec获取用户表示（监督信号）
        with torch.no_grad():
            # mode='log_only' 表示只获取最后一个位置的用户表示
            # 这个表示包含了用户的历史偏好信息，作为LLM学习的目标
            log_emb = self.recsys.model(u, seq, pos, neg, mode='log_only')

        # 步骤2: 逐样本构造训练数据
        for i in range(len(u)):
            # 获取当前样本的目标物品（序列最后一个正样本）
            target_item_id = pos[i][-1]
            target_item_title = self.find_item_text_single(target_item_id, title_flag=True, description_flag=False)

            # 构造用户历史交互的文本表示
            # seq[i][seq[i]>0]: 过滤掉padding(0)，只保留真实交互
            # 10: 最多使用最近10个交互
            interact_text, interact_ids = self.make_interact_text(seq[i][seq[i]>0], 10, u[i])

            # 构造候选物品的文本表示（1个正样本 + 3个负样本）
            candidate_num = 4
            candidate_text, candidate_ids = self.make_candidate_text(seq[i][seq[i]>0], candidate_num, target_item_id, target_item_title, task='RecTask')

            # 构造用户历史的完整prompt
            # 格式：描述 + 历史序列 + 生成指令
            input_text = ''
            input_text += 'This user has made a series of purchases in the following order: '
            input_text += interact_text  # 包含 [HistoryEmb] 占位符的历史文本
            input_text += ". Based on this sequence of purchases, generate user representation token:[UserOut]"

            # 添加到批次数据中
            text_input.append(input_text)
            candidates_pos += candidate_text  # 扩展候选物品文本列表

            # 步骤3: 准备嵌入向量用于替换特殊token
            # 历史物品嵌入：从CF-SRec获取并投影到LLM空间
            interact_embs.append(self.item_emb_proj((self.get_item_emb(interact_ids))))

            # 候选物品嵌入：同样投影到LLM空间
            candidate_embs_pos.append(self.item_emb_proj((self.get_item_emb([candidate_ids]))).squeeze(0))

        # 步骤4: 整合批次数据
        # 将所有候选物品嵌入拼接成一个张量
        candidate_embs = torch.cat(candidate_embs_pos)

        # 构造LLM训练样本字典
        samples = {
            'text_input': text_input,           # 用户历史prompt列表
            'log_emb': log_emb,                 # CF-SRec的用户表示（监督信号）
            'candidates_pos': candidates_pos,   # 候选物品prompt列表
            'interact': interact_embs,          # 历史物品嵌入列表（用于替换[HistoryEmb]）
            'candidate_embs': candidate_embs,   # 候选物品嵌入张量（用于替换[HistoryEmb]）
        }

        # 步骤5: LLM前向传播和损失计算
        # mode=0 对应 train_mode0，计算推荐损失和对齐损失
        loss, rec_loss, match_loss = self.llm(samples, mode=0)

        # 输出训练日志（可配置打印间隔）
        if getattr(self.args, 'log_interval', 50) > 0 and (step % self.args.log_interval) == 0:
            print("LLMRec model loss in epoch {}/{} iteration {}/{}: {}".format(epoch, total_epoch, step, total_step, rec_loss))
            print("LLMRec model Matching loss (Enhanced KD) in epoch {}/{} iteration {}/{}: {}".format(epoch, total_epoch, step, total_step, match_loss))

        # 步骤6: 反向传播和参数更新
        loss.backward()

        # 特殊硬件优化（如果使用神经网络处理器）
        if self.args.nn_parameter:
            htcore.mark_step()

        optimizer.step()

        if self.args.nn_parameter:
            htcore.mark_step()
        
    
    def split_into_batches(self, itemnum, m):
        """
        将物品ID范围分割成批次，用于批量处理

        Args:
            itemnum (int): 物品总数
            m (int): 每个批次的大小

        Returns:
            list: 批次列表，每个批次包含连续的物品ID
        """
        numbers = list(range(1, itemnum + 1))  # 物品ID从1开始（0是padding）
        batches = [numbers[i:i + m] for i in range(0, itemnum, m)]
        return batches

    def generate_batch(self, data):
        """
        推理阶段的批量生成方法 - 生成所有物品表示并进行推荐评估

        这个方法实现两个主要功能：
        1. 预计算所有物品的LLM表示（如果尚未计算）
        2. 为给定用户生成推荐并计算评估指标

        核心思想：
        - 首次调用时，批量生成所有物品的LLM表示并缓存
        - 后续调用直接使用缓存的表示进行快速推荐
        - 通过用户表示与物品表示的相似度进行排序推荐

        Args:
            data (tuple): 评估数据 (u, seq, pos, neg, rank, candi_set, files)
                - u: 用户ID列表
                - seq: 用户历史序列
                - pos: 正样本物品ID
                - neg: 负样本物品ID（当前未使用）
                - rank: 排序相关信息
                - candi_set: 候选物品集合
                - files: 文件相关信息（当前未使用）

        Returns:
            float: NDCG@10 评估指标
        """
        # 阶段1: 预计算所有物品的LLM表示（仅在首次调用时执行）
        if self.all_embs == None:
            # 根据模型和数据集动态调整批次大小，平衡内存使用和计算效率
            batch_ = 128  # 默认批次大小

            # Llama模型内存占用较大，减小批次
            if self.args.llm in ['llama', 'llama-3b']:
                batch_ = 64

            # 大型数据集进一步减小批次避免OOM
            if self.args.rec_pre_trained_data == 'Electronics' or self.args.rec_pre_trained_data == 'Books':
                batch_ = 64
                if self.args.llm in ['llama', 'llama-3b']:
                    batch_ = 32

            # 将所有物品分批处理
            batches = self.split_into_batches(self.item_num, batch_)
            self.all_embs = []  # 存储所有物品表示的列表
            max_input_length = 512  # 降低长度以减少显存占用

            # 逐批次生成物品表示
            for bat in tqdm(batches):
                candidate_text = []   # 当前批次的物品文本
                candidate_ids = []    # 当前批次的物品ID
                candidate_embs = []   # 当前批次的物品嵌入

                # 为批次中的每个物品构造文本prompt
                for neg_candidate in bat:
                    # 构造物品的标准prompt格式
                    item_title = self.find_item_text_single(neg_candidate, title_flag=True, description_flag=False)
                    candidate_text.append('The item title and item embedding are as follows: ' + item_title + "[HistoryEmb], then generate item representation token:[ItemOut]")
                    candidate_ids.append(neg_candidate)

                # 批量处理当前批次（无梯度计算，节省内存）
                with torch.no_grad():
                    # 文本tokenization
                    candi_tokens = self.llm.llm_tokenizer(
                        candidate_text,
                        return_tensors="pt",
                        padding="longest",
                        truncation=True,
                        max_length=max_input_length,
                    ).to(self.device)

                    # 获取物品的CF-SRec嵌入并投影到LLM空间
                    candidate_embs.append(self.item_emb_proj((self.get_item_emb(candidate_ids))))

                    # 获取初始词嵌入
                    candi_embeds = self.llm.llm_model.get_input_embeddings()(candi_tokens['input_ids'])

                    # 关键步骤：用物品嵌入替换[HistoryEmb]，用CLS向量替换[ItemOut]
                    candi_embeds = self.llm.replace_out_token_all_infer(candi_tokens, candi_embeds, token=['[ItemOut]', '[HistoryEmb]'], embs={'[HistoryEmb]': candidate_embs[0]})

                    # LLM前向传播（使用混合精度加速）
                    with torch.amp.autocast('cuda'):
                        candi_outputs = self.llm.llm_model.model(
                            inputs_embeds=candi_embeds,
                            output_hidden_states=True
                        )

                        # 提取[ItemOut]位置的隐藏状态作为物品表示
                        indx = self.llm.get_embeddings(candi_tokens, '[ItemOut]')
                        item_outputs = torch.cat([candi_outputs.hidden_states[-1][i, indx[i]].mean(axis=0).unsqueeze(0) for i in range(len(indx))])

                        # 通过预测头映射到最终表示空间
                        item_outputs = self.llm.pred_item(item_outputs)

                    # 保存当前批次的物品表示
                    self.all_embs.append(item_outputs)

                    # 显式删除大型张量释放内存
                    del candi_outputs
                    del item_outputs

            # 将所有批次的物品表示拼接成完整的物品表示矩阵
            # 形状: [item_num, hidden_dim]
            self.all_embs = torch.cat(self.all_embs)
            
        # 阶段2: 为当前批次用户生成推荐
        u, seq, pos, neg, rank, candi_set, files = data
        original_seq = seq.copy()  # 保留原始序列（调试用）

        # 初始化批次数据容器
        text_input = []     # 用户历史的文本prompt
        interact_embs = []  # 用户历史物品的嵌入
        candidate = []      # 每个用户的候选物品ID列表

        # 无梯度模式进行推理
        with torch.no_grad():
            # 逐用户构造推荐数据
            for i in range(len(u)):
                candidate_embs = []  # 当前用户的候选物品嵌入（未使用）

                # 获取目标物品信息（用于评估）
                target_item_id = pos[i]
                target_item_title = self.find_item_text_single(target_item_id, title_flag=True, description_flag=False)

                # 构造用户历史交互文本（最多10个最近交互）
                interact_text, interact_ids = self.make_interact_text(seq[i][seq[i]>0], 10, u[i])

                # 生成候选物品集合（1个正样本 + 99个负样本）
                candidate_num = 100
                candidate_ids = self.make_candidate(seq[i][seq[i]>0], candidate_num, target_item_id, target_item_title, candi_set)
                candidate.append(candidate_ids)

                # 构造用户的完整prompt
                input_text = ''
                input_text += 'This user has made a series of purchases in the following order: '
                input_text += interact_text  # 包含[HistoryEmb]占位符
                input_text += ". Based on this sequence of purchases, generate user representation token:[UserOut]"

                text_input.append(input_text)

                # 准备历史物品嵌入用于替换[HistoryEmb]
                interact_embs.append(self.item_emb_proj((self.get_item_emb(interact_ids))))
                

            # 阶段3: 批量生成用户表示
            max_input_length = 512

            # 对用户历史文本进行tokenization
            llm_tokens = self.llm.llm_tokenizer(
                text_input,
                return_tensors="pt",
                padding="longest",
                truncation=True,
                max_length=max_input_length,
            ).to(self.device)

            # 获取初始词嵌入
            inputs_embeds = self.llm.llm_model.get_input_embeddings()(llm_tokens['input_ids'])

            # 关键步骤：用历史物品嵌入替换[HistoryEmb]，用CLS向量替换[UserOut]
            inputs_embeds = self.llm.replace_out_token_all(llm_tokens, inputs_embeds, token=['[UserOut]', '[HistoryEmb]'], embs={'[HistoryEmb]': interact_embs})

            # LLM前向传播生成用户表示
            with torch.cuda.amp.autocast():
                outputs = self.llm.llm_model.model(
                    inputs_embeds=inputs_embeds,
                    output_hidden_states=True
                )

                # 提取[UserOut]位置的隐藏状态作为用户表示
                indx = self.llm.get_embeddings(llm_tokens, '[UserOut]')
                user_outputs = torch.cat([outputs.hidden_states[-1][i, indx[i]].mean(axis=0).unsqueeze(0) for i in range(len(indx))])

                # 通过预测头映射到最终用户表示空间
                user_outputs = self.llm.pred_user(user_outputs)

                # 阶段4: 逐用户进行推荐评估
                for i in range(len(candidate)):
                    # 获取当前用户的候选物品表示（从预计算的all_embs中索引）
                    # 注意：物品ID从1开始，数组索引从0开始，所以需要减1
                    item_outputs = self.all_embs[np.array(candidate[i]) - 1]

                    # 计算用户表示与候选物品表示的相似度得分
                    # 使用矩阵乘法计算内积相似度
                    logits = torch.mm(item_outputs, user_outputs[i].unsqueeze(0).T).squeeze(-1)

                    # 转换为排序分数（负号使得相似度高的物品排名靠前）
                    logits = -1 * logits

                    # 计算目标物品（正样本，位于候选列表第一位）的排名
                    # argsort().argsort() 技巧：第一次argsort得到排序索引，第二次得到每个元素的排名
                    rank = logits.argsort().argsort()[0].item()

                    # 计算评估指标
                    # NDCG@10 和 Hit@10
                    if rank < 10:
                        self.NDCG += 1 / np.log2(rank + 2)  # NDCG计算公式
                        self.HT += 1

                    # NDCG@20 和 Hit@20
                    if rank < 20:
                        self.NDCG_20 += 1 / np.log2(rank + 2)
                        self.HIT_20 += 1

                    # 累计评估用户数
                    self.users += 1

        return self.NDCG
                
    def extract_emb(self, data):
        """
        用户表示提取方法 - 仅提取用户嵌入而不进行推荐评估

        这个方法专门用于提取用户的LLM表示，通常用于：
        1. 用户表示的可视化分析
        2. 用户聚类和相似度分析
        3. 下游任务的特征提取
        4. 模型表示质量的定性分析

        与 generate_batch 的区别：
        - generate_batch: 提取用户表示 + 进行推荐评估
        - extract_emb: 仅提取用户表示，保存到 self.extract_embs_list

        Args:
            data (tuple): 用户数据 (u, seq, pos, neg, original_seq, rank, files)
                - u: 用户ID列表
                - seq: 用户历史序列
                - pos: 正样本物品ID（当前未使用）
                - neg: 负样本物品ID（当前未使用）
                - original_seq: 原始序列（当前未使用）
                - rank: 排序信息（当前未使用）
                - files: 文件信息（当前未使用）

        Returns:
            int: 固定返回0（表示成功完成）

        副作用：
            - 将提取的用户表示添加到 self.extract_embs_list 中
            - 表示会被移动到CPU并分离梯度以节省内存
        """
        u, seq, pos, neg, original_seq, rank, files = data

        # 初始化数据容器
        text_input = []     # 用户历史的文本prompt
        interact_embs = []  # 用户历史物品的嵌入
        candidate = []      # 候选物品（当前未使用）

        # 无梯度模式进行推理
        with torch.no_grad():
            # 逐用户构造文本表示
            for i in range(len(u)):
                # 构造用户历史交互文本（最多10个最近交互）
                interact_text, interact_ids = self.make_interact_text(seq[i][seq[i]>0], 10, u[i])

                # 构造用户的完整prompt
                input_text = ''
                input_text += 'This user has made a series of purchases in the following order: '
                input_text += interact_text  # 包含[HistoryEmb]占位符
                input_text += ". Based on this sequence of purchases, generate user representation token:[UserOut]"

                text_input.append(input_text)

                # 准备历史物品嵌入用于替换[HistoryEmb]
                interact_embs.append(self.item_emb_proj((self.get_item_emb(interact_ids))))

            # 批量处理用户文本
            max_input_length = 512

            # 对用户历史文本进行tokenization
            llm_tokens = self.llm.llm_tokenizer(
                text_input,
                return_tensors="pt",
                padding="longest",
                truncation=True,
                max_length=max_input_length,
            ).to(self.device)

            # 获取初始词嵌入
            inputs_embeds = self.llm.llm_model.get_input_embeddings()(llm_tokens['input_ids'])

            # 用历史物品嵌入替换[HistoryEmb]，用CLS向量替换[UserOut]
            inputs_embeds = self.llm.replace_out_token_all(llm_tokens, inputs_embeds, token=['[UserOut]', '[HistoryEmb]'], embs={'[HistoryEmb]': interact_embs})

            # LLM前向传播生成用户表示
            with torch.cuda.amp.autocast():
                outputs = self.llm.llm_model.model(
                    inputs_embeds=inputs_embeds,
                    output_hidden_states=True
                )

                # 提取[UserOut]位置的隐藏状态作为用户表示
                indx = self.llm.get_embeddings(llm_tokens, '[UserOut]')
                user_outputs = torch.cat([outputs.hidden_states[-1][i, indx[i]].mean(axis=0).unsqueeze(0) for i in range(len(indx))])

                # 通过预测头映射到最终用户表示空间
                user_outputs = self.llm.pred_user(user_outputs)

                # 保存用户表示到列表中（移动到CPU并分离梯度以节省GPU内存）
                self.extract_embs_list.append(user_outputs.detach().cpu())

        return 0
