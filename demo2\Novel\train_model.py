#!/usr/bin/env python3
"""
模型训练脚本

专门用于训练推荐模型的独立脚本。

使用示例:
    # 使用默认配置训练
    python train_model.py --dataset Movies_and_TV

    # 使用自定义配置训练
    python train_model.py --config config/collaborative_config.yaml

    # 指定训练参数
    python train_model.py --dataset Movies_and_TV --batch-size 64 --learning-rate 0.001 --epochs 20

    # 从检查点恢复训练
    python train_model.py --config config/collaborative_config.yaml --resume checkpoints/model_epoch_10.pth

    # 分布式训练
    python train_model.py --config config/collaborative_config.yaml --distributed
"""

import argparse
import logging
import sys
import torch
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from experiments import ExperimentRunner, ConfigManager
from datasets import DatasetManager

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
    ]
)

logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="模型训练脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    # 基本参数
    parser.add_argument(
        '--config', '-c',
        type=str,
        help='配置文件路径'
    )
    
    parser.add_argument(
        '--dataset', '-d',
        type=str,
        help='数据集名称'
    )
    
    parser.add_argument(
        '--experiment-name', '-n',
        type=str,
        help='实验名称'
    )
    
    # 训练参数
    parser.add_argument(
        '--batch-size', '-b',
        type=int,
        help='批次大小'
    )
    
    parser.add_argument(
        '--learning-rate', '-lr',
        type=float,
        help='学习率'
    )
    
    parser.add_argument(
        '--epochs', '-e',
        type=int,
        help='训练轮数'
    )
    
    parser.add_argument(
        '--device',
        type=str,
        help='训练设备 (cuda:0, cpu等)'
    )
    
    parser.add_argument(
        '--output-dir', '-o',
        type=str,
        help='输出目录'
    )
    
    # 模型参数
    parser.add_argument(
        '--hidden-units',
        type=int,
        help='隐藏单元数'
    )
    
    parser.add_argument(
        '--num-blocks',
        type=int,
        help='Transformer块数量'
    )
    
    parser.add_argument(
        '--num-heads',
        type=int,
        help='注意力头数量'
    )
    
    parser.add_argument(
        '--dropout-rate',
        type=float,
        help='Dropout率'
    )
    
    # 训练选项
    parser.add_argument(
        '--resume',
        type=str,
        help='从检查点恢复训练'
    )
    
    parser.add_argument(
        '--distributed',
        action='store_true',
        help='使用分布式训练'
    )
    
    parser.add_argument(
        '--fp16',
        action='store_true',
        help='使用混合精度训练'
    )
    
    parser.add_argument(
        '--gradient-accumulation-steps',
        type=int,
        default=1,
        help='梯度累积步数'
    )
    
    parser.add_argument(
        '--save-steps',
        type=int,
        help='保存检查点的步数间隔'
    )
    
    parser.add_argument(
        '--eval-steps',
        type=int,
        help='评估的步数间隔'
    )
    
    parser.add_argument(
        '--logging-steps',
        type=int,
        help='日志记录的步数间隔'
    )
    
    # 早停参数
    parser.add_argument(
        '--patience',
        type=int,
        help='早停耐心值'
    )
    
    parser.add_argument(
        '--min-delta',
        type=float,
        help='早停最小改进值'
    )
    
    # 其他选项
    parser.add_argument(
        '--seed',
        type=int,
        help='随机种子'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='干运行（不实际训练）'
    )
    
    return parser.parse_args()


def setup_logging(verbose: bool):
    """设置日志级别"""
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    else:
        logging.getLogger().setLevel(logging.INFO)


def create_experiment_runner(args) -> ExperimentRunner:
    """创建实验运行器"""
    config_path = args.config
    experiment_name = args.experiment_name
    
    # 如果指定了数据集但没有配置文件，创建数据集特定的配置
    if args.dataset and not config_path:
        config_manager = ConfigManager()
        if not experiment_name:
            experiment_name = f"train_{args.dataset}"
        
        config_path = config_manager.create_dataset_experiment(args.dataset, experiment_name)
        logger.info(f"Created dataset-specific config: {config_path}")
    
    # 创建实验运行器
    runner = ExperimentRunner(config_path, experiment_name)
    
    # 应用命令行参数覆盖
    apply_config_overrides(runner, args)
    
    return runner


def apply_config_overrides(runner: ExperimentRunner, args):
    """应用配置覆盖"""
    # 训练参数
    if args.batch_size:
        runner.config['training']['batch_size'] = args.batch_size
    
    if args.learning_rate:
        runner.config['training']['learning_rate'] = args.learning_rate
    
    if args.epochs:
        runner.config['training']['num_epochs'] = args.epochs
    
    if args.device:
        runner.config['device'] = args.device
    
    if args.output_dir:
        runner.config['training']['output_dir'] = args.output_dir
    
    # 模型参数
    if args.hidden_units:
        runner.config['small_model']['hidden_units'] = args.hidden_units
    
    if args.num_blocks:
        runner.config['small_model']['num_blocks'] = args.num_blocks
    
    if args.num_heads:
        runner.config['small_model']['num_heads'] = args.num_heads
    
    if args.dropout_rate:
        runner.config['small_model']['dropout_rate'] = args.dropout_rate
    
    # 训练选项
    if args.fp16:
        runner.config['optimization']['fp16'] = True
    
    if args.gradient_accumulation_steps:
        runner.config['optimization']['gradient_accumulation_steps'] = args.gradient_accumulation_steps
    
    if args.save_steps:
        runner.config['training']['save_steps'] = args.save_steps
    
    if args.eval_steps:
        runner.config['training']['eval_steps'] = args.eval_steps
    
    if args.logging_steps:
        runner.config['training']['logging_steps'] = args.logging_steps
    
    # 早停参数
    if args.patience:
        runner.config['training']['patience'] = args.patience
    
    if args.min_delta:
        runner.config['training']['min_delta'] = args.min_delta
    
    # 其他参数
    if args.seed:
        runner.config['seed'] = args.seed


def check_prerequisites(runner: ExperimentRunner):
    """检查训练前提条件"""
    dataset_name = runner.config['data']['dataset']
    
    # 检查数据集是否已准备
    if not runner.dataset_manager.is_dataset_processed(dataset_name):
        logger.error(f"Dataset {dataset_name} not prepared. Please run data preparation first:")
        logger.error(f"  python prepare_data.py --dataset {dataset_name}")
        return False
    
    # 检查设备可用性
    device = runner.config.get('device', 'cpu')
    if device.startswith('cuda') and not torch.cuda.is_available():
        logger.warning(f"CUDA not available, falling back to CPU")
        runner.config['device'] = 'cpu'
    
    return True


def print_training_info(runner: ExperimentRunner):
    """打印训练信息"""
    config = runner.config
    
    print("Training Configuration:")
    print("=" * 50)
    print(f"Experiment Name: {config['experiment_name']}")
    print(f"Dataset: {config['data']['dataset']}")
    print(f"Device: {config.get('device', 'cpu')}")
    print(f"Batch Size: {config['training']['batch_size']}")
    print(f"Learning Rate: {config['training']['learning_rate']}")
    print(f"Epochs: {config['training']['num_epochs']}")
    print(f"Output Dir: {config['training']['output_dir']}")
    
    print("\nModel Configuration:")
    print(f"Small Model Type: {config['small_model']['model_type']}")
    print(f"Hidden Units: {config['small_model']['hidden_units']}")
    print(f"Num Blocks: {config['small_model']['num_blocks']}")
    print(f"Num Heads: {config['small_model']['num_heads']}")
    print(f"Dropout Rate: {config['small_model']['dropout_rate']}")
    
    print(f"\nLarge Model Type: {config['large_model']['model_type']}")
    print(f"LLM Model: {config['large_model']['llm_model']}")
    print(f"Use LoRA: {config['large_model']['use_lora']}")
    
    print("=" * 50)


def main():
    """主函数"""
    args = parse_args()
    setup_logging(args.verbose)
    
    try:
        # 创建实验运行器
        runner = create_experiment_runner(args)
        
        # 检查前提条件
        if not check_prerequisites(runner):
            sys.exit(1)
        
        # 打印训练信息
        if not args.dry_run:
            print_training_info(runner)
        
        # 干运行模式
        if args.dry_run:
            print("Dry run mode - configuration validated successfully")
            print_training_info(runner)
            return
        
        # 开始训练
        logger.info("Starting model training...")
        
        if args.resume:
            logger.info(f"Resuming training from: {args.resume}")
            # TODO: 实现从检查点恢复训练的逻辑
        
        # 运行训练
        results = runner.run_training_only()
        
        print("\nTraining completed successfully!")
        print(f"Final training loss: {results.get('final_loss', 'N/A')}")
        print(f"Best validation metric: {results.get('best_metric', 'N/A')}")
        print(f"Training time: {results.get('training_time', 'N/A'):.2f} seconds")
        print(f"Results saved to: {runner.experiment_dir}")
        
        # 如果有验证结果，显示最佳指标
        if 'best_metrics' in results:
            print("\nBest Validation Metrics:")
            for metric, value in results['best_metrics'].items():
                if isinstance(value, (int, float)):
                    print(f"  {metric}: {value:.4f}")
    
    except KeyboardInterrupt:
        logger.info("Training interrupted by user")
        sys.exit(1)
    
    except Exception as e:
        logger.error(f"Training failed: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
