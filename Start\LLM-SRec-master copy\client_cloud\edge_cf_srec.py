"""
端侧CF-SRec隐私保护适配器

本模块基于现有的SASRec模型，实现端侧的隐私保护功能：
1. 复用原始RecSys封装，保持SASRec完整架构和参数规模
2. 添加隐私保护的用户表示生成功能
3. 集成差分隐私保护机制
4. 提供安全的端云通信接口

设计原则：
- 适配器模式：不修改原始SASRec代码，通过适配器扩展功能
- 代码复用：最大化复用models/recsys_model.py的现有实现
- 功能分离：隐私保护功能独立于推荐模型核心逻辑
- 向后兼容：保持与原始LLM-SRec项目的兼容性
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import pickle
import json
from pathlib import Path
import logging

# 复用原始LLM-SRec项目的模块
from models.recsys_model import RecSys
from privacy import EdgeCloudPrivacyProtector, PrivacyConfig


class EdgeCFSRecAdapter(nn.Module):
    """
    端侧CF-SRec隐私保护适配器

    设计理念：
    - 基于适配器模式，包装原始RecSys而不修改其代码
    - 添加隐私保护层，确保用户数据安全
    - 提供端云协同所需的用户表示生成功能
    - 保持与原始SASRec模型的完全兼容性

    核心功能：
    1. 用户表示生成：从用户序列生成64维安全表示向量
    2. 隐私保护：应用差分隐私和加密算法
    3. 本地推荐：提供快速的端侧推荐服务
    4. 安全传输：准备安全的云端传输数据包
    """

    def __init__(self, args, privacy_level: str = 'high'):
        """
        初始化端侧CF-SRec适配器

        Args:
            args: 原始LLM-SRec项目的模型配置参数
            privacy_level: 隐私保护级别 ('low', 'medium', 'high', 'ultra')
        """
        super().__init__()
        self.args = args
        self.device = args.device
        self.privacy_level = privacy_level
        self.logger = logging.getLogger(__name__)

        # 复用原始RecSys封装（完全不修改原始代码）
        self.logger.info("加载原始SASRec模型...")
        self.recsys = RecSys(args.recsys, args.rec_pre_trained_data, self.device)
        self.item_num = self.recsys.item_num
        self.hidden_units = self.recsys.hidden_units

        # 复用原始数据加载逻辑
        self.logger.info("加载物品文本信息...")
        with open(f'./SeqRec/data_{args.rec_pre_trained_data}/text_name_dict.json.gz', 'rb') as ft:
            self.text_name_dict = pickle.load(ft)

        # 初始化隐私保护组件
        privacy_config = PrivacyConfig(
            user_repr_dim=64,  # 固定64维用户表示
            privacy_level=privacy_level
        )
        self.privacy_protector = EdgeCloudPrivacyProtector(privacy_config)

        # 用户表示投影层（SASRec hidden_units → 64维）
        self.user_repr_projector = nn.Sequential(
            nn.Linear(self.hidden_units, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 64),
            nn.LayerNorm(64)
        )

        # 初始化投影层权重
        self._initialize_projector()

        # 本地推荐缓存和统计
        self.local_item_embeddings = None
        self.recommendation_cache = {}
        self.privacy_statistics = {
            'total_requests': 0,
            'privacy_protected_requests': 0,
            'average_noise_level': 0.0
        }

        self.logger.info(f"端侧CF-SRec适配器初始化完成 (隐私级别: {privacy_level})")

    def _initialize_projector(self):
        """初始化用户表示投影层权重"""
        for module in self.user_repr_projector:
            if isinstance(module, nn.Linear):
                nn.init.xavier_normal_(module.weight)
                nn.init.zeros_(module.bias)
        
        # 初始化投影层参数
        self._initialize_projector()
    
    def _initialize_projector(self):
        """初始化用户表示投影层"""
        for module in self.user_repr_projector:
            if isinstance(module, nn.Linear):
                nn.init.xavier_normal_(module.weight)
                nn.init.zeros_(module.bias)
    
    def generate_user_representation(
        self, 
        user_sequence: List[int], 
        user_id: Optional[str] = None
    ) -> torch.Tensor:
        """
        生成用户表示向量（端侧处理，原始数据不离开设备）
        
        Args:
            user_sequence: 用户交互序列
            user_id: 用户ID（可选）
            
        Returns:
            64维用户表示向量
        """
        self.eval()
        
        with torch.no_grad():
            # 准备输入数据
            if len(user_sequence) > self.args.maxlen:
                user_sequence = user_sequence[-self.args.maxlen:]
            
            # 填充序列到固定长度
            padded_sequence = [0] * (self.args.maxlen - len(user_sequence)) + user_sequence
            
            # 转换为tensor
            log_seqs = np.array([padded_sequence])
            
            # 使用SASRec生成序列表示
            # 使用'log_only'模式获取用户序列的最后位置表示
            sasrec_repr = self.recsys.model(
                user_ids=np.array([0]),  # 用户ID在SASRec中不使用
                log_seqs=log_seqs,
                pos_seqs=np.array([[0]]),  # 占位符
                neg_seqs=np.array([[0]]),  # 占位符
                mode='log_only'
            )  # 输出形状: [1, hidden_units]
            
            # 投影到64维
            user_repr_64d = self.user_repr_projector(sasrec_repr)  # [1, 64]
            user_repr_64d = user_repr_64d.squeeze(0)  # [64]
            
            return user_repr_64d
    
    def generate_secure_user_representation(
        self, 
        user_sequence: List[int], 
        user_id: str
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        生成安全的用户表示（应用隐私保护）
        
        Args:
            user_sequence: 用户交互序列
            user_id: 用户ID
            
        Returns:
            (加密传输包, 隐私风险评估)
        """
        # 生成原始用户表示
        user_repr = self.generate_user_representation(user_sequence, user_id)
        
        # 应用隐私保护并准备传输
        encrypted_package, risk_assessment = self.privacy_protector.protect_and_transmit(
            user_repr, user_id, len(user_sequence)
        )
        
        return encrypted_package, risk_assessment
    
    def local_recommend(
        self, 
        user_sequence: List[int], 
        top_k: int = 10,
        exclude_seen: bool = True
    ) -> List[int]:
        """
        本地快速推荐（端侧处理，保护隐私）
        
        Args:
            user_sequence: 用户交互序列
            top_k: 推荐物品数量
            exclude_seen: 是否排除已交互物品
            
        Returns:
            推荐物品ID列表
        """
        self.eval()
        
        with torch.no_grad():
            # 准备候选物品（排除已交互物品）
            if exclude_seen:
                seen_items = set(user_sequence)
                candidate_items = [i for i in range(1, self.item_num + 1) if i not in seen_items]
            else:
                candidate_items = list(range(1, self.item_num + 1))
            
            # 限制候选物品数量以提高效率
            if len(candidate_items) > 1000:
                candidate_items = np.random.choice(candidate_items, 1000, replace=False).tolist()
            
            # 准备输入数据
            if len(user_sequence) > self.args.maxlen:
                user_sequence = user_sequence[-self.args.maxlen:]
            
            padded_sequence = [0] * (self.args.maxlen - len(user_sequence)) + user_sequence
            log_seqs = np.array([padded_sequence])
            
            # 使用SASRec进行预测
            item_indices = np.array([candidate_items])
            scores = self.recsys.model.predict(
                user_ids=np.array([0]),
                log_seqs=log_seqs,
                item_indices=item_indices
            )  # [1, num_candidates]
            
            # 获取top-k推荐
            scores = scores.squeeze(0)  # [num_candidates]
            top_indices = torch.topk(scores, min(top_k, len(candidate_items))).indices
            recommendations = [candidate_items[idx] for idx in top_indices.cpu().numpy()]
            
            return recommendations
    
    def get_item_info(self, item_id: int) -> Dict[str, Any]:
        """
        获取物品信息（本地缓存）
        
        Args:
            item_id: 物品ID
            
        Returns:
            物品信息字典
        """
        if item_id in self.text_name_dict:
            title, description = self.text_name_dict[item_id]
            return {
                'item_id': item_id,
                'title': title,
                'description': description
            }
        else:
            return {
                'item_id': item_id,
                'title': f'Item_{item_id}',
                'description': 'No description available'
            }
    
    def batch_generate_user_representations(
        self, 
        user_sequences: List[List[int]], 
        user_ids: List[str]
    ) -> List[torch.Tensor]:
        """
        批量生成用户表示
        
        Args:
            user_sequences: 用户序列列表
            user_ids: 用户ID列表
            
        Returns:
            用户表示向量列表
        """
        assert len(user_sequences) == len(user_ids), "序列数量和用户ID数量不匹配"
        
        user_representations = []
        
        for user_seq, user_id in zip(user_sequences, user_ids):
            user_repr = self.generate_user_representation(user_seq, user_id)
            user_representations.append(user_repr)
        
        return user_representations
    
    def update_privacy_config(self, new_config: PrivacyConfig):
        """
        更新隐私保护配置
        
        Args:
            new_config: 新的隐私配置
        """
        self.privacy_protector = EdgeCloudPrivacyProtector(new_config)
    
    def get_privacy_statistics(self) -> Dict[str, Any]:
        """
        获取隐私保护统计信息
        
        Returns:
            隐私统计信息
        """
        risk_history = self.privacy_protector.risk_assessor.risk_history
        
        if not risk_history:
            return {
                'total_requests': 0,
                'average_risk_score': 0.0,
                'risk_level_distribution': {},
                'common_risk_factors': []
            }
        
        # 计算统计信息
        total_requests = len(risk_history)
        average_risk_score = np.mean([r['risk_score'] for r in risk_history])
        
        # 风险级别分布
        risk_levels = [r['risk_level'] for r in risk_history]
        risk_level_distribution = {
            level: risk_levels.count(level) / total_requests
            for level in ['low', 'medium', 'high']
        }
        
        # 常见风险因素
        all_risk_factors = []
        for r in risk_history:
            all_risk_factors.extend(r['risk_factors'])
        
        from collections import Counter
        common_risk_factors = Counter(all_risk_factors).most_common(5)
        
        return {
            'total_requests': total_requests,
            'average_risk_score': float(average_risk_score),
            'risk_level_distribution': risk_level_distribution,
            'common_risk_factors': common_risk_factors
        }
    
    def save_model(self, save_path: str):
        """
        保存模型（仅保存投影层参数，SASRec参数保持不变）
        
        Args:
            save_path: 保存路径
        """
        save_dict = {
            'user_repr_projector': self.user_repr_projector.state_dict(),
            'args': self.args,
            'privacy_config': self.privacy_protector.config
        }
        
        torch.save(save_dict, save_path)
    
    def load_model(self, load_path: str):
        """
        加载模型
        
        Args:
            load_path: 加载路径
        """
        checkpoint = torch.load(load_path, map_location=self.device)
        
        self.user_repr_projector.load_state_dict(checkpoint['user_repr_projector'])
        
        if 'privacy_config' in checkpoint:
            self.update_privacy_config(checkpoint['privacy_config'])


def create_edge_cf_srec(args, privacy_level: str = 'high') -> EdgeCFSRecAdapter:
    """
    创建端侧CF-SRec适配器的便捷函数

    Args:
        args: 原始LLM-SRec项目的模型配置参数
        privacy_level: 隐私保护级别 ('low', 'medium', 'high', 'ultra')

    Returns:
        EdgeCFSRecAdapter实例
    """
    return EdgeCFSRecAdapter(args, privacy_level)


# 为了向后兼容，提供原始类名的别名
EdgeCFSRec = EdgeCFSRecAdapter
