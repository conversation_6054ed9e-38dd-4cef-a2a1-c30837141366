# 端云协同推荐系统运行指南 (双3090)

## ⚠️ 重要提醒
**所有LLM-SRec训练命令必须包含 `--save_checkpoint_every 1` 参数！**
- ✅ 确保生成checkpoint文件
- ✅ 支持断点续训功能
- ✅ 防止训练中断丢失进度

## � 环境准备

```bash
# 创建环境
conda create -n edge_cloud_rec python=3.10
conda activate edge_cloud_rec
pip install -r requirements.txt

# 设置双GPU
export CUDA_VISIBLE_DEVICES=0,1
```

## 📊 阶段一：SASRec预训练

```bash
cd SeqRec/sasrec
python main.py --dataset Movies_and_TV --device 0 --num_epochs 200
cd ../..
```

## 🤖 阶段二：LLM-SRec训练

### 双GPU训练（必须包含checkpoint参数）
```bash
python main.py --train --multi_gpu --world_size 2 --llm llama-3b --rec_pre_trained_data Movies_and_TV --save_dir model_train_multi --batch_size 20 --num_epochs 10 --save_checkpoint_every 1
```

### 断点续训 🆕
```bash
python main.py --train --multi_gpu --world_size 2 --llm llama-3b --rec_pre_trained_data Movies_and_TV --save_dir model_train_multi --batch_size 20 --num_epochs 10 --resume ./checkpoints/model_train_multi/checkpoint_latest.pt --save_checkpoint_every 1
```

### 提取表示
```bash
python main.py --extract --llm llama-3b --rec_pre_trained_data Movies_and_TV --save_dir model_train_multi
```

### ⚠️ 重要提醒
**所有训练命令必须包含 `--save_checkpoint_every 1`，否则：**
- ❌ 不会生成checkpoint文件
- ❌ 训练中断后无法恢复
- ❌ 无法使用断点续训功能

## 🌐 阶段三：端云协同系统部署

```bash
# 测试端云协同系统
python demo_edge_cloud_system.py

# 验证隐私保护功能
python -c "from privacy import EdgeCloudPrivacyProtector; print('✅ 隐私保护模块正常')"
```

## 🔄 阶段四：推理阶段

**训练过程中已自动进行推理评估！**

### 独立推理（可选）
```bash
python main.py --extract --llm llama-3b --rec_pre_trained_data Movies_and_TV --save_dir model_train_multi
```

## 💾 阶段五：Checkpoint管理 🆕

### 验证checkpoint生成
```bash
# 训练开始后检查checkpoint目录
ls -la checkpoints/model_train_multi/

# 应该看到类似输出：
# checkpoint_epoch_1.pt
# checkpoint_epoch_2.pt
# checkpoint_latest.pt -> checkpoint_epoch_2.pt
```

### 查看checkpoint详情
```bash
python resume_training.py --list --save_dir=model_train_multi
```

### 使用辅助脚本恢复
```bash
python resume_training.py --latest --save_dir=model_train_multi --dataset=Movies_and_TV --gpu_id=0,1
```

### 手动检查checkpoint内容
```bash
python -c "
import torch
checkpoint = torch.load('./checkpoints/model_train_multi/checkpoint_latest.pt', map_location='cpu')
print(f'Checkpoint epoch: {checkpoint[\"epoch\"]}')
print(f'Available keys: {list(checkpoint.keys())}')
"
```

## 📈 阶段六：结果分析

```bash
# 查看训练结果
cat models/model_train_multi/best/Movies_and_TV_llama-3b_*_results.txt

# 查看checkpoint状态
ls -la checkpoints/model_train_multi/
```

## ❌ 常见错误

### 错误1：忘记添加checkpoint参数
```bash
# ❌ 错误命令（没有checkpoint）
python main.py --train --multi_gpu --world_size 2 --llm llama-3b --rec_pre_trained_data Movies_and_TV --save_dir model_train_multi --batch_size 20 --num_epochs 10

# ✅ 正确命令（有checkpoint）
python main.py --train --multi_gpu --world_size 2 --llm llama-3b --rec_pre_trained_data Movies_and_TV --save_dir model_train_multi --batch_size 20 --num_epochs 10 --save_checkpoint_every 1
```

### 错误2：checkpoint目录不存在
```bash
# 检查并创建checkpoint目录
mkdir -p checkpoints/model_train_multi
```

### 错误3：断点续训找不到文件
```bash
# 检查checkpoint文件是否存在
ls -la checkpoints/model_train_multi/checkpoint_latest.pt

# 如果不存在，检查其他epoch的checkpoint
ls -la checkpoints/model_train_multi/
```




