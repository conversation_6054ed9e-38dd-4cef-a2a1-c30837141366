#!/usr/bin/env python3
"""
测试双向知识蒸馏功能

验证端云协同系统中的双向知识蒸馏是否正常工作
"""

import asyncio
import logging
import sys
import os

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from client_cloud import SystemConfig, RecommendationMode, create_edge_cloud_system

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_bidirectional_distillation():
    """测试双向知识蒸馏功能"""
    
    logger.info("🧪 开始测试双向知识蒸馏功能")
    
    # 创建系统配置
    config = SystemConfig(
        device="cuda:0" if os.path.exists("/proc/driver/nvidia") else "cpu",
        rec_pre_trained_data="Movies_and_TV",
        default_recommendation_mode=RecommendationMode.COLLABORATIVE,
        privacy_level="medium",
        enable_knowledge_distillation=True,
        enable_bidirectional_distillation=True,  # 启用双向蒸馏
        distillation_interval=5  # 降低触发阈值以便测试
    )
    
    try:
        # 创建并初始化系统
        logger.info("📦 创建端云协同系统...")
        system = await create_edge_cloud_system(config)
        
        # 验证双向蒸馏框架是否正确初始化
        if system.distillation_framework:
            distillation_stats = system.distillation_framework.get_distillation_statistics()
            logger.info("✅ 双向蒸馏框架初始化成功:")
            logger.info(f"   蒸馏模式: {distillation_stats.get('distillation_mode', 'unknown')}")
            logger.info(f"   双向蒸馏: {distillation_stats.get('bidirectional_enabled', False)}")
        else:
            logger.error("❌ 蒸馏框架初始化失败")
            return False
        
        # 生成测试用户序列
        test_users = []
        for i in range(10):
            user_id = f"test_user_{i:03d}"
            # 生成随机序列（模拟用户行为）
            import random
            sequence_length = random.randint(5, 15)
            user_sequence = [random.randint(1, 1000) for _ in range(sequence_length)]
            test_users.append((user_id, user_sequence))
        
        logger.info(f"🎯 生成 {len(test_users)} 个测试用户")
        
        # 执行推荐以触发蒸馏
        logger.info("🚀 开始执行推荐以触发双向蒸馏...")
        
        for i, (user_id, user_sequence) in enumerate(test_users):
            try:
                # 执行推荐
                result = await system.recommend(
                    user_sequence=user_sequence,
                    user_id=user_id,
                    num_recommendations=5,
                    mode=RecommendationMode.COLLABORATIVE
                )
                
                logger.info(f"✅ 用户 {user_id} 推荐完成 ({i+1}/{len(test_users)})")
                
                # 检查是否触发了蒸馏
                if len(system.distillation_queue) >= config.distillation_interval:
                    logger.info("🔄 即将触发双向知识蒸馏...")
                
            except Exception as e:
                logger.warning(f"⚠️ 用户 {user_id} 推荐失败: {e}")
        
        # 等待可能的蒸馏完成
        await asyncio.sleep(2)
        
        # 获取最终统计信息
        system_stats = await system.get_system_statistics()
        
        logger.info("📊 双向蒸馏测试结果:")
        logger.info(f"   总推荐次数: {system_stats['metrics']['total_recommendations']}")
        logger.info(f"   蒸馏触发次数: {system_stats['metrics']['knowledge_distillation_count']}")
        
        if 'distillation_stats' in system_stats:
            distillation_stats = system_stats['distillation_stats']
            logger.info(f"   蒸馏模式: {distillation_stats.get('distillation_mode', 'unknown')}")
            logger.info(f"   双向蒸馏启用: {distillation_stats.get('bidirectional_enabled', False)}")
            logger.info(f"   蒸馏轮数: {distillation_stats.get('total_epochs', 0)}")
            logger.info(f"   最佳准确率: {distillation_stats.get('best_accuracy', 0):.4f}")
            
            if distillation_stats.get('bidirectional_enabled', False):
                logger.info("🎉 双向蒸馏功能正常工作!")
                logger.info(f"   反向蒸馏权重: {distillation_stats.get('reverse_distillation_weight', 0)}")
                logger.info(f"   互信息权重: {distillation_stats.get('mutual_info_weight', 0)}")
                logger.info(f"   对比学习权重: {distillation_stats.get('contrastive_weight', 0)}")
            else:
                logger.warning("⚠️ 双向蒸馏未启用")
        
        # 关闭系统
        await system.close()
        
        logger.info("✅ 双向知识蒸馏测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def compare_distillation_modes():
    """对比单向和双向蒸馏的效果"""
    
    logger.info("📊 开始对比单向和双向蒸馏效果")
    
    results = {}
    
    for bidirectional in [False, True]:
        mode_name = "双向蒸馏" if bidirectional else "单向蒸馏"
        logger.info(f"🧪 测试 {mode_name}...")
        
        config = SystemConfig(
            device="cpu",  # 使用CPU以确保兼容性
            rec_pre_trained_data="Movies_and_TV",
            default_recommendation_mode=RecommendationMode.COLLABORATIVE,
            privacy_level="medium",
            enable_knowledge_distillation=True,
            enable_bidirectional_distillation=bidirectional,
            distillation_interval=3
        )
        
        try:
            system = await create_edge_cloud_system(config)
            
            # 执行少量推荐
            for i in range(5):
                user_id = f"compare_user_{i}"
                user_sequence = [1, 2, 3, 4, 5]  # 简单序列
                
                await system.recommend(
                    user_sequence=user_sequence,
                    user_id=user_id,
                    num_recommendations=3
                )
            
            # 等待蒸馏完成
            await asyncio.sleep(1)
            
            # 收集结果
            stats = await system.get_system_statistics()
            results[mode_name] = {
                'distillation_count': stats['metrics']['knowledge_distillation_count'],
                'total_recommendations': stats['metrics']['total_recommendations']
            }
            
            if 'distillation_stats' in stats:
                results[mode_name].update({
                    'distillation_mode': stats['distillation_stats'].get('distillation_mode', 'unknown'),
                    'best_accuracy': stats['distillation_stats'].get('best_accuracy', 0)
                })
            
            await system.close()
            
        except Exception as e:
            logger.error(f"❌ {mode_name} 测试失败: {e}")
            results[mode_name] = {'error': str(e)}
    
    # 输出对比结果
    logger.info("📈 蒸馏模式对比结果:")
    for mode, result in results.items():
        logger.info(f"  {mode}:")
        for key, value in result.items():
            logger.info(f"    {key}: {value}")
    
    return results


async def main():
    """主函数"""
    logger.info("🚀 开始双向知识蒸馏测试")
    
    # 测试1: 基础双向蒸馏功能
    success = await test_bidirectional_distillation()
    
    if success:
        logger.info("✅ 基础测试通过")
        
        # 测试2: 对比不同蒸馏模式
        await compare_distillation_modes()
    else:
        logger.error("❌ 基础测试失败")
    
    logger.info("🎉 所有测试完成")


if __name__ == "__main__":
    asyncio.run(main())
